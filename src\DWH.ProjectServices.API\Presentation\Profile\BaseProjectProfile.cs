﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Presentation.Profile
{
    public class BaseProjectProfile : AutoMapper.Profile
    {
        public BaseProjectProfile()
        {
            CreateMap<BaseProjectResponse, BaseProject>()
            .ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
            .ReverseMap();
            CreateMap<BaseProjectGetResponse, BaseProject>()
            .ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
            .ReverseMap();
            CreateMap<BaseProjectCreateRequest, BaseProject>()
            .ForMember(dest => dest.QCProjects, opt => opt.MapFrom(src => new QCProject
            {
                IsAutoLoad = src.IsAutoLoad,
                SQCMode = src.SQCMode,
                IsAutomatedPriceCheck = src.IsAutomatedPriceCheck,
                ResetCorrectionTypeId = src.ResetCorrectionTypeId
            }))
            .ForMember(dest => dest.CreatedWhen, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedWhen, opt => opt.Ignore())
            .ForMember(dest => dest.DeletedWhen, opt => opt.Ignore()).ReverseMap();
            CreateMap<BaseProjectPredecessorRequest, BaseProjectPredecessor>().ReverseMap();
            CreateMap<BaseProjectProductGroupModelDto, BaseProjectProductGroup>().ReverseMap();
            CreateMap<BaseProject, BaseProjectEntity>().ReverseMap();
            CreateMap<BaseProject, BaseProjectPredecessorResponse>().ReverseMap();
            CreateMap<BaseProject, BaseProjectPredecessorResponse>().ReverseMap();
            CreateMap<BaseProjectsLists, BaseProjectListRequest>()
            .ForMember(dest => dest.Users, opt => opt.MapFrom(src => src.Usernames))
            .ForMember(dest => dest.StartDate, opt => opt.MapFrom(src => src.StartingDate))
            .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.EndingDate))
            .ForMember(dest => dest.BaseProjectIds, opt => opt.MapFrom(src => src.BaseProjectIDs))
            .ForMember(dest => dest.QCProjectIds, opt => opt.MapFrom(src => src.QCProjectIDs))
            .ForMember(dest => dest.IncludeDeleted, opt => opt.MapFrom(src => src.Deleted))
            .ReverseMap();
            CreateMap<BaseProjectEditRequest, BaseProject>().ReverseMap();
            CreateMap<BaseProjectEditResponse, BaseProject>().ReverseMap();
            CreateMap<QCProjectEntity, QCProject>().ReverseMap();
            CreateMap<BaseProjectPredecessorModelDto, BaseProjectPredecessor>().ReverseMap();
            CreateMap<BaseProjectDeletes, BaseProjectDeleteRequest>().ReverseMap();
            CreateMap<BaseProjectPanel, BaseProjectPanelEntity>().ReverseMap();
            CreateMap<ProjectSubTypeResponse, ProjectSubTypes>().ReverseMap();
            CreateMap<ProjectSubType, ProjectSubTypes>().ReverseMap();
            CreateMap<BaseProjectListResponse, BaseProjectLists>().ReverseMap();
            CreateMap<BaseProjectNameandIdResponse, BaseProjectNameandIds>().ReverseMap();
            CreateMap<BaseProjectNameandIds, BaseProjectEntity>().ReverseMap();
            CreateMap<BaseProjectNameandIdLists, BaseProjectNameAndIdListRequest>().ReverseMap();
            CreateMap<BaseProjectNameandIdListResponse, ProjectLists>().ReverseMap();
            CreateMap<BaseProjectCountryRequest, BaseProjectCountries>().ReverseMap();
        }
    }
}
