﻿using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit.Extensions.AssertExtensions;
using BootstrapAPI.Core.Exception.Instances;
using System.Net;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Models;
using Microsoft.AspNetCore.Http;
using static DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.BaseProjectRepository;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using Microsoft.Extensions.DependencyInjection;
using OpenTelemetry;
using DWH.ProjectServices.API.Presentation.Profile;
using DWH.ProjectServices.API.Models.Dtos;
using System.Text.Json;


namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class BaseProjectServiceTests
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;
        private readonly Mock<ILogger<BaseProjectService>> _loggerStub;
        private readonly Mock<IBaseProjectRepository> _baseProjectRepositoryStub;
        private readonly BaseProjectService _service;
        private readonly Mock<IOperationHelper> _operationHelper;
        private readonly Mock<IRoleHelper> _roleHelper;
        private readonly Mock<ISecurityHelper> _securityHelper;
        private readonly Mock<IOutBoxItemRepository> _mockoutBoxItemRepository;
        private readonly Mock<IRetailerSeperationService> _retailerSeperationService;

        private enum Countries
        {
            France = 17
        }

        public BaseProjectServiceTests()
        {
            _fixture = new Fixture();
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new Infrastructure.Persistence.Profile.BaseProjectsProfile());
                mc.AddProfile(new BaseProjectProfile());
            });
            _mapper = mappingConfig.CreateMapper();
            _loggerStub = new Mock<ILogger<BaseProjectService>>();
            _baseProjectRepositoryStub = new Mock<IBaseProjectRepository>();
            _mockoutBoxItemRepository = new Mock<IOutBoxItemRepository>();
            _operationHelper = new Mock<IOperationHelper>();
            _roleHelper = new Mock<IRoleHelper>();
            _securityHelper = new Mock<ISecurityHelper>();
            _retailerSeperationService = new Mock<IRetailerSeperationService>();

            _service = new BaseProjectService(_mapper, _baseProjectRepositoryStub.Object, _mockoutBoxItemRepository.Object,
                _operationHelper.Object, _loggerStub.Object, _roleHelper.Object, _securityHelper.Object);
        }


        [Fact]
        public async Task AddAsync_When_SuccessfullyAdded_Expect_SuccessfulResult()
        {
            // Arrange
            var masterUserIds = new List<int> { };
            const int AnnualPeriodicityId = 7;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId) 
                .Create();

            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            _roleHelper
                .Setup(helper => helper.GetMasterUsers())
                .ReturnsAsync(masterUserIds);

            // Act
            var result = await _service.AddAsync(baseProject);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(baseProject);
            result.QCProjects.QCPeriods.Should().NotBeNull();
        }


        [Fact]
        public async Task AddAsync_When_SuccessfullyAdded_Expect_SuccessfulResultWithIDAS()
        {
            // Arrange
            var masterUserIds = new List<int> { };
            const int AnnualPeriodicityId = 7;
            const int PosPanelId = (int)PanelType.IDAS;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PanelId, PosPanelId)
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .Create();

            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            _roleHelper
                .Setup(helper => helper.GetMasterUsers())
                .ReturnsAsync(masterUserIds);

            // Act
            var result = await _service.AddAsync(baseProject);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(baseProject);
            result.QCProjects.Should().BeNull();
        }

        [Fact]
        public async Task AddAsync_When_BCRConflictsFound_Expect_BadHttpRequestException()
        {
            // Arrange
            const int AnnualPeriodicityId = 7;
            const int PosPanelId = (int)PanelType.POS;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PanelId, PosPanelId)
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .With(bp => bp.ProductGroups, new List<BaseProjectProductGroup>
                {
            new BaseProjectProductGroup { ProductGroupId = 1 },
            new BaseProjectProductGroup { ProductGroupId = 2 }
                })
                .Create();

            int[] pgIds = baseProject.ProductGroups.Select(pg => pg.ProductGroupId).ToArray();
            var bcrConflictResult = new BaseChannelRearrangements
            {
                Success = false,
                NumberOfConflicts = 3
            };

            _operationHelper
                .Setup(helper => helper.GetBCRDetails("/v1/base-channel-rearrangement/check", pgIds))
                .ReturnsAsync(bcrConflictResult);

            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            // Act

            var exception = await _service.Invoking(s => s.AddAsync(baseProject))
                                            .Should().ThrowAsync<BadHttpRequestException>();

            // Assert
            exception.NotBeNull();
            exception.Which.Message.Should().Contain("BCR Conflicts found. Number of conflicts 3");
        }


        [Fact]
        public async Task CreateQCProjectModelAsync_WhenInvalidPeriodicityId_ThrowsException()
        {
            // Arrange
            const int InvalidPeriodicityId = 999;
            var baseProject = new BaseProject
            {
                PeriodicityId = InvalidPeriodicityId,
                QCProjects = new QCProject()
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(InvalidPeriodicityId))
                .ReturnsAsync(new List<ShiftedPeriodResponse>
                {
            new ShiftedPeriodResponse(1, "Period 1", "P1"),
            new ShiftedPeriodResponse(2, "Period 2", "P2")
                });

            Dictionary.PeriodicityMapping.Remove(InvalidPeriodicityId);

            // Act
            Func<Task> act = async () => await _service.AddAsync(baseProject);

            // Assert
            var exception = await act.Should().ThrowAsync<ArgumentOutOfRangeException>();
            exception.And.ParamName.Should().Be(nameof(baseProject.PeriodicityId));
            exception.And.Message.Should().Contain($"Not expected PeriodicityId value: {InvalidPeriodicityId}");
        }


        [Fact]
        public async Task CreateQCProjectModelAsync_WhenApiFails_ThrowsException()
        {
            // Arrange
            const int PeriodicityId = 1;  
            var baseProject = new BaseProject
            {
                PeriodicityId = PeriodicityId
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(PeriodicityId))
                .ThrowsAsync(new HttpRequestException());

            // Act
            Func<Task> act = async () => await _service.AddAsync(baseProject);

            // Assert
            await act.Should().ThrowAsync<HttpRequestException>();
        }

        [Fact]
        public async void GetAllAsync_WhenCalled_WithValidCountryId_Returns_UndeletedBaseProjects()
        {

            _fixture.Customize<BaseProject>(b => b.With(x => x.Id, 1)
                                                 .With(x => x.Name, "Text1")
                                                 .With(x => x.CountryId, ((int)Countries.France))
                                                 .With(x => x.Predecessors,
                                                 _fixture.CreateMany<BaseProjectPredecessor>(5).ToList())
                                                 .With(x => x.ProductGroups,
                                                 _fixture.CreateMany<BaseProjectProductGroup>(5).ToList()));
            var baseProject1 = _fixture.Create<BaseProject>();

            _fixture.Customize<BaseProject>(b => b.With(x => x.Id, 2)
                                                   .With(x => x.Name, "Text2")
                                                   .With(x => x.CountryId, (int)Countries.France)
                                                  .With(x => x.Predecessors,
                                                  _fixture.CreateMany<BaseProjectPredecessor>(2).ToList())
                                                  .With(x => x.ProductGroups,
                                                  _fixture.CreateMany<BaseProjectProductGroup>(1).ToList()));




            var baseProject2 = _fixture.Create<BaseProject>();

            var baseProjects = new List<BaseProject>();
            baseProjects.Add(baseProject1);
            baseProjects.Add(baseProject2);

            var bpPredecessorRequest = _fixture.Create<BaseProjectPredecessor>();
            _baseProjectRepositoryStub.Setup(b => b.GetAllAsync(bpPredecessorRequest)).ReturnsAsync(baseProjects);

            var result = await _service.GetAllAsync(bpPredecessorRequest);
            var productGroups1 = result.Where(x => x.Id == 1).SelectMany(x => x.ProductGroups);
            var predecessors1 = result.Where(x => x.Id == 1).SelectMany(x => x.Predecessors);

            var productGroups2 = result.Where(x => x.Id == 2).SelectMany(x => x.ProductGroups);
            var predecessors2 = result.Where(x => x.Id == 2).SelectMany(x => x.Predecessors);

            result.ShouldNotBeNull();
            result.Count().Should().Be(2);
            productGroups1.Count().Should().Be(5);
            predecessors1.Count().Should().Be(5);
            productGroups2.Count().Should().Be(1);
            predecessors2.Count().Should().Be(2);


        }


        [Fact]
        public async void GetAllAsync_WhenCalled_WithInvalidCountryId_Throws_Exception()
        {
            var bpPredecessorRequest = _fixture.Create<BaseProjectPredecessor>();
            bpPredecessorRequest.CountryId = 1000;

            _baseProjectRepositoryStub.Setup(b => b.GetAllAsync(bpPredecessorRequest)).ReturnsAsync(Enumerable.Empty<BaseProject>());

            await _service.Invoking(s => s.GetAllAsync(bpPredecessorRequest))
                                            .Should().ThrowAsync<EntityNotExistsException>();
        }

        

        [Fact]
        public async void GetAsync_WhenCalled_WithValidId_Returns_UndeletedBaseProjects()
        {

            _fixture.Customize<BaseProject>(b => b.With(x => x.Id, 1)
                                                 .With(x => x.Name, "Text1")
                                                 .With(x => x.CountryId, (int)Countries.France)
                                                 .With(x => x.Predecessors,
                                                 _fixture.CreateMany<BaseProjectPredecessor>(5).ToList())
                                                 .With(x => x.ProductGroups,
                                                 _fixture.CreateMany<BaseProjectProductGroup>(5).ToList()));
            var baseProject1 = _fixture.Create<BaseProject>();

            _baseProjectRepositoryStub.Setup(b => b.GetAsync(1)).ReturnsAsync(baseProject1);


            var result = await _service.GetAsync(1);

            result.ShouldNotBeNull();
            result.Predecessors.Count().Should().Be(5);
            result.ProductGroups.Count().Should().Be(5);

        }
        [Fact]
        public async Task GetAsync_WhenCalled_WithInvalidId_Throws_Exception()
        {
            // Arrange
            var unrealId = 1000;

            _baseProjectRepositoryStub.Setup(b => b.GetAsync(unrealId))
                                      .ThrowsAsync(new EntityNotExistsException($"No Base Project exists with Id {unrealId}",
                                                                                "Base Project", unrealId));

            // Act
            var act = async () => await _service.GetAsync(unrealId);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>()
                      .WithMessage($"ENTITY_NOT_EXISTS");
        }


        [Fact]
        public async Task GetAsyncList_WhenCalled_Returns_UndeletedBaseProjects()
        {
            // Arrange
            var baseProjectListRequest = new BaseProjectsLists
            {
                Id = 1,
                Name = "TestName",
                CountryIds = new int[] { 1, 2, 3 },
                ProductGroupIds = new long[] { 101, 102, 103 },
                PeriodicityIds = new int[] { 10, 20 },
                PanelIds = new long[] { 201, 202, 203 },
                DataTypeIds = new long[] { 1, 2, 3 },
                PurposeIds = new long[] { 1, 2, 3 },
                TypeIds = new int[] { 100, 200 },
                Usernames = new string[] { "testuser" },
                QCProjectIDs = new int[] { 1, 2, 3 },
                BaseProjectIDs = new int[] { 100, 200 },
                StartingDate = null,
                EndingDate = null
            };

            var baseProjects = new List<BaseProject>
                                    {
                                        new BaseProject { Id = 1, Name = "Project1" },
                                        new BaseProject { Id = 2, Name = "Project2" },
                                        new BaseProject { Id = 3, Name = "Project3" }
                                    };
       
            // Act
            Func<Task> action = async () => await _service.GetAsyncList(baseProjectListRequest);

            // Assert
            await action.Should().NotThrowAsync<EntityNotExistsException>(); 
            var result = await _service.GetAsyncList(baseProjectListRequest);
            result.Should().NotBeNull();
           
        }

        [Fact]
        public async Task GetAsyncList_WhenCalled_Throws_Exception_When_No_BaseProjects_Found()
        {
            // Arrange
            var baseProjectListRequestDto = _fixture.Create<BaseProjectsLists>();

            _baseProjectRepositoryStub.Setup(b => b.GetAsyncList(baseProjectListRequestDto))
                                      .ThrowsAsync(new EntityNotExistsException("No Base Projects found", "Base Project"));

            // Act
            Func<Task> act = async () => await _service.GetAsyncList(baseProjectListRequestDto);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>()
                    .WithMessage("ENTITY_NOT_EXISTS");
        }

        [Fact]
        public async Task UpdateAsync_WhenCalled_WithInvalidIdinModel_Throws_Exception()
        {
            // Arrange
            var baseProjectId = 1;
            var baseProjectEditRequest = _fixture.Create<BaseProject>();

            _baseProjectRepositoryStub.Setup(b => b.GetAsync(baseProjectId))
                                      .ThrowsAsync(new EntityNotExistsException($"No Base Project exists with Id {baseProjectId}",
                                                                                 "Base Project", baseProjectId));

            // Act
            Func<Task> act = async () => await _service.UpdateAsync(baseProjectId, baseProjectEditRequest);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>()
                    .WithMessage($"ENTITY_NOT_EXISTS");
        }


        [Fact]
        public async void UpdateAsync_WhenCalled_WithValidModel_Updates_Successfully()
        {
            var baseProjectId = 1;
            const int AnnualPeriodicityId = 7;
            var baseProjectEditRequest = _fixture.Create<BaseProject>();
            var pgswithRBBP = new List<RBPGDependencies>();
            var deletedPGs= new List<int>();

            var expectedResultRepository = _fixture.Build<BaseProject>()
                .With(bp=> bp.Id,baseProjectId)
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .With(bp => bp.PanelId, (int)PanelType.POS)
                .Create();
            var expectedResultService = new BaseProject();
            expectedResultService.Id = baseProjectId;
            expectedResultService.Predecessors = new List<BaseProjectPredecessor>();
            expectedResultService.ProductGroups = new List<BaseProjectProductGroup>();
            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);
            _baseProjectRepositoryStub.Setup(b => b.GetQCPeriodAsync(baseProjectId)).ReturnsAsync(baseProjectEditRequest.QCProjects.QCPeriods.ToList());

            _baseProjectRepositoryStub.Setup(b => b.GetAsync(baseProjectId)).ReturnsAsync(expectedResultRepository);

            _baseProjectRepositoryStub.Setup(b => b.UpdateAsync(baseProjectId, baseProjectEditRequest, It.IsAny<bool>())).ReturnsAsync(expectedResultRepository);

            _baseProjectRepositoryStub.Setup(b => b.CheckProductGroupRBBPDependency(It.IsAny<List<int>>(),It.IsAny<int>())).ReturnsAsync(pgswithRBBP);

            var result = await _service.UpdateAsync(baseProjectId, baseProjectEditRequest);

            result.Id.ShouldEqual(expectedResultService.Id);
        }

        [Fact]
        public async void DeleteAsync_WhenCalled_WithValidModel_Deletes_Successfully()
        {
            var baseProjectDeleteDto = _fixture.Create<BaseProjectDeletes>();
            var baseProjectDeleteIds = _fixture.Create<int>();
            var projectDependencies = new ProjectsDependencies
            {
                RBBaseProjectIds = new int[] { },
                ProductionProjectIds = new int[] { },
                ReportingProjectIds = new int[] { }
            };
            var responses = new List<Dependencies>
            {
                new Dependencies("4","7",(int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),null),
                new Dependencies("5","6",(int)HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError.ToString(),null),
                new Dependencies("6","5",(int)HttpStatusCode.BadRequest, HttpStatusCode.BadRequest.ToString(),null),
                new Dependencies("7","4",(int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(),null)
            };
            var qcStatusBPs = new List<long> { };
            _baseProjectRepositoryStub.Setup(b => b.GetQCPeriodWithQCStatus(It.IsAny<QCStatuses>())).ReturnsAsync(qcStatusBPs);

            _baseProjectRepositoryStub.Setup(b => b.CheckDependencies(It.IsAny<int>())).ReturnsAsync(projectDependencies);

            _baseProjectRepositoryStub.Setup(b => b.DeleteAsync(It.IsAny<List<int>>(), It.IsAny<string>()))
            .ReturnsAsync(responses);
            var result = await _service.DeleteAsync(baseProjectDeleteDto);

            result.ShouldNotBeNull();
            result.Count.Should().Be(4);
            result.Contains(new Dependencies("4","5", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(),null));
            result.Contains(new Dependencies("6","5", (int)HttpStatusCode.BadRequest, HttpStatusCode.BadRequest.ToString(), null));
            result.Contains(new Dependencies("7","5", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(), null));
        }

        [Fact]
        public async Task DeleteAsync_AllDependenciesReturned_ShouldReturnBadRequestResponses()
        {
            // Arrange
            var baseProjectDeleteDto = _fixture.Create<BaseProjectDeletes>();
            var projectDependencies = new ProjectsDependencies
            {
                RBBaseProjectIds = new int[] { 1, 2 },
                ProductionProjectIds = new int[] { 1, 2 },
                ReportingProjectIds = new int[] { 1, 2 }
            };
            var qcStatusBPs = new List<long> { };
            _baseProjectRepositoryStub.Setup(b => b.GetQCPeriodWithQCStatus(It.IsAny<QCStatuses>())).ReturnsAsync(qcStatusBPs);

            foreach (var id in baseProjectDeleteDto.Ids)
            {
                _baseProjectRepositoryStub.Setup(repo => repo.CheckDependencies(id))
                                          .ReturnsAsync(projectDependencies);
            }

            var deleteResponses = baseProjectDeleteDto.Ids.Select(id => new Dependencies(id.ToString(), id.ToString(), (int)HttpStatusCode.OK, "", projectDependencies))
                                                          .ToList();
            _baseProjectRepositoryStub.Setup(repo => repo.DeleteAsync(baseProjectDeleteDto.Ids, baseProjectDeleteDto.DeletedBy))
                                      .ReturnsAsync(deleteResponses);

            // Act
            var result = await _service.DeleteAsync(baseProjectDeleteDto);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(baseProjectDeleteDto.Ids.Count);

            // Check for BadRequest responses with correct messages
            foreach (var id in baseProjectDeleteDto.Ids)
            {
                result.Should().ContainSingle(r => r.BaseProjectId == id.ToString() &&
                                                   r.StatusCode == (int)HttpStatusCode.BadRequest);
                result.Should().HaveCount(3);
            }

        }

        [Fact]
        public async void DeletAsync_WhenCalled_WithValidModel_Throws_NoException()
        {
            var baseProjectDelete = _fixture.Create<BaseProjectDeletes>();
            var projectDependencies = new ProjectsDependencies
            {
                RBBaseProjectIds = new int[] { },
                ProductionProjectIds = new int[] { },
                ReportingProjectIds = new int[] {}
            };
            var qcStatusBPs = new List<long> { };
            _baseProjectRepositoryStub.Setup(b => b.GetQCPeriodWithQCStatus(It.IsAny<QCStatuses>())).ReturnsAsync(qcStatusBPs);

            _baseProjectRepositoryStub.Setup(b => b.CheckDependencies(It.IsAny<int>())).ReturnsAsync(projectDependencies);

            _baseProjectRepositoryStub.Setup(b => b.DeleteAsync(It.IsAny<List<int>>(), It.IsAny<string>())).ReturnsAsync(new List<Dependencies>());

            var exception = await Record.ExceptionAsync(() => _service.DeleteAsync(baseProjectDelete));

            exception.ShouldBeNull();

        }

        [Fact]
        public async Task GetBCRDetails_SuccessfulResponse_ReturnsData()
        {
            // Arrange
            var masterUserIds = new List<int> { };
            const int AnnualPeriodicityId = 7;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .With(bp => bp.PanelId, (int)PanelType.POS)
                .Create();

            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            var expectedBCRDetails = new BaseChannelRearrangements
            {
                Success = true,
                NumberOfConflicts = 0
            };

            _operationHelper
                .Setup(helper => helper.GetBCRDetails(It.IsAny<string>(), It.IsAny<int[]>()))
                .ReturnsAsync(expectedBCRDetails);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            _roleHelper
                .Setup(helper => helper.GetMasterUsers())
                .ReturnsAsync(masterUserIds);

            // Act
            var result = await _service.AddAsync(baseProject);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(baseProject);
            result.QCProjects.QCPeriods.Should().NotBeNull();
        }

        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_WhenCalled_ReturnsSuccessResponse()
        {
            // Arrange
            var baseProjectsLists = _fixture.Create<BaseProjectNameandIdLists>();
            var baseProjects = _fixture.CreateMany<BaseProjectNameandIds>(5).ToList();

            _baseProjectRepositoryStub.Setup(repo => repo.GetAsyncListBaseProjects(baseProjectsLists))
                                      .ReturnsAsync(baseProjects);

            // Act
            var result = await _service.GetAsyncListBaseProjects(baseProjectsLists);

            // Assert
            result.Should().NotBeNull();
            result.Records.Should().NotBeNullOrEmpty();
            result.Records.Count.Should().Be(5);
            result.Counts.Should().Be(5);
        }

        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_WhenCalled_WithNoDataFound_ThrowsEntityNotExistsException()
        {
            // Arrange
            var irBaseProjectsLists = _fixture.Create<BaseProjectNameandIdLists>();

            _baseProjectRepositoryStub.Setup(repo => repo.GetAsyncListBaseProjects(irBaseProjectsLists))
                                      .ThrowsAsync(new EntityNotExistsException("No Base Projects found", "Base Project"));

            // Act
            Func<Task> act = async () => await _service.GetAsyncListBaseProjects(irBaseProjectsLists);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>()
                     .WithMessage("ENTITY_NOT_EXISTS");
        }

        [Fact]
        public async Task DeleteAsync_WhenQCStatusExists_AddsBadRequestDependencies()
        {
            // Arrange
            var baseProjectDeleteDto = _fixture.Create<BaseProjectDeletes>();
            var baseProjectIds = baseProjectDeleteDto.Ids;

            var qcStatuses = new QCStatuses
            {
                BaseProjectIds = baseProjectIds
            };

            var qcStatusBps = new List<long> { baseProjectIds.First() };

            var projectDependencies = new ProjectsDependencies
            {
                RBBaseProjectIds = new int[] { baseProjectIds.First() + 1 },
                ProductionProjectIds = new int[] { },
                ReportingProjectIds = new int[] { }
            };

            var deleteResponses = new List<Dependencies>
            {
                new Dependencies(baseProjectIds.Last().ToString(), baseProjectIds.Last().ToString(), (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(), null)
            };

            _baseProjectRepositoryStub.Setup(repo => repo.GetQCPeriodWithQCStatus(It.IsAny<QCStatuses>()))
                .ReturnsAsync(qcStatusBps);

            _baseProjectRepositoryStub.Setup(repo => repo.CheckDependencies(It.IsAny<int>()))
                .ReturnsAsync(projectDependencies);

            _baseProjectRepositoryStub.Setup(repo => repo.DeleteAsync(It.IsAny<List<int>>(), It.IsAny<string>()))
                .ReturnsAsync(deleteResponses);

            // Act
            var result = await _service.DeleteAsync(baseProjectDeleteDto);

            // Assert
            result.ShouldNotBeNull();
            result.Count.Should().Be(3);

            result.Should().Contain(dep => dep.BaseProjectId == qcStatusBps.First().ToString() &&
                                            dep.StatusCode == (int)HttpStatusCode.BadRequest &&
                                            dep.Dependency.QCStatusPeriodId != 0);
        }

        [Fact]
        public async Task GetAsync_When_RepositoryReturnsNull_Expect_EmptyNull()
        {
            // Arrange
            List<int> expectedResult = new List<int> { };
            var baseProjectCountryRequest = new BaseProjectCountryRequest
            {
            };
            var baseProjectCountryModel = _mapper.Map<BaseProjectCountries>(baseProjectCountryRequest);

            _baseProjectRepositoryStub
                .Setup(r => r.GetByBaseProjectIdAsync(baseProjectCountryModel))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _service.GetAsync(baseProjectCountryModel);
            result.Should().HaveCount(0);
        }

        [Fact]
        public async Task GetAsync_When_RepositoryReturnsNonEmptyList_Expect_SameList()
        {
            // Arrange
            var expectedResult = new List<int> { 1, 2, 3 };
            var baseProjectCountryRequest = new BaseProjectCountryRequest
            {
                BaseProjectIds = new int[] { 1, 2, 3 },
                CountryIds = new int[] { 15 }
            };
            var baseProjectCountryModel = _mapper.Map<BaseProjectCountries>(baseProjectCountryRequest);

            _baseProjectRepositoryStub
                .Setup(r => r.GetByBaseProjectIdAsync(baseProjectCountryModel))
                .ReturnsAsync(expectedResult);


            // Act
            var result = await _service.GetAsync(baseProjectCountryModel);

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public async Task AddAsync_When_MasterUserIdsReturned_Expect_UsersAssignedToProject()
        {
            // Arrange
            var masterUserIds = new List<int> { 1, 2, 3 };
            var assignUserResponse = _fixture.Create<AssignUsersToProjectResponse>();
            const int AnnualPeriodicityId = 7;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .Create();
            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            var expectedQCProjects = new QCProject { Id = 123 };
            baseProject.QCProjects = expectedQCProjects;

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            _roleHelper
                .Setup(helper => helper.GetMasterUsers())
                .ReturnsAsync(masterUserIds);

            _securityHelper
                .Setup(helper => helper.AssignUsersToProject(It.IsAny<AssignUsersToProjectDetails>(), It.IsAny<string>()))
                .ReturnsAsync(assignUserResponse);

            // Act
            var result = await _service.AddAsync(baseProject);

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(baseProject);
        }

        [Fact]
        public async Task AddAsync_When_GetMasterUsersThrowsException_Expect_ExceptionPropagated()
        {
            // Arrange
            const int AnnualPeriodicityId = 7;
            var baseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.PeriodicityId, AnnualPeriodicityId)
                .Create();
            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "Period 1", "P1"),
                new ShiftedPeriodResponse(2, "Period 2", "P2")
            };

            var expectedQCProjects = new QCProject { Id = 123 };
            baseProject.QCProjects = expectedQCProjects;

            _operationHelper
                .Setup(helper => helper.GetPeriodsAsync(It.IsAny<int>()))
                .ReturnsAsync(expectedPeriods);

            _baseProjectRepositoryStub
                .Setup(repo => repo.AddAsync(baseProject))
                .ReturnsAsync(baseProject);

            var exceptionMessage = "Error calling API for master users";
            _roleHelper
                .Setup(helper => helper.GetMasterUsers())
                .ThrowsAsync(new HttpRequestException(exceptionMessage));

            // Act
            Func<Task> act = async () => await _service.AddAsync(baseProject);

            // Assert
            await act.Should().ThrowAsync<HttpRequestException>()
                .WithMessage($"*{exceptionMessage}*");
        }

        [Fact]
        public async Task UpdateAsync_WhenDeletedPGsHaveDataOrRBBPDependencies_ThrowsInvalidOperationException()
        {
            // Arrange
            var baseProjectId = 1;
            var baseProjectEditRequest = _fixture.Create<BaseProject>();

            var existingBaseProject = _fixture.Build<BaseProject>()
                .With(bp => bp.Id, baseProjectId)
                .With(bp => bp.ProductGroups, new List<BaseProjectProductGroup>
                {
            new() { ProductGroupId = 100 },
            new() { ProductGroupId = 200 } // Will be removed
                })
                .Create();

            baseProjectEditRequest.ProductGroups = new List<BaseProjectProductGroup>
            {
                new() { ProductGroupId = 100 } // PG 200 is removed
            };

            _baseProjectRepositoryStub
                .Setup(repo => repo.GetAsync(baseProjectId))
                .ReturnsAsync(existingBaseProject);

            _baseProjectRepositoryStub
                .Setup(repo => repo.CheckDataLoading(It.IsAny<List<int>>(), baseProjectId))
                .ReturnsAsync(new[] { 200 });

            _baseProjectRepositoryStub
                .Setup(repo => repo.CheckProductGroupRBBPDependency(It.IsAny<List<int>>(), baseProjectId))
                .ReturnsAsync(new List<RBPGDependencies>
                {
            new() { ProductGroupId = 200, ProductGroupDesc = "PG 200", RbProjectId = 999 }
                });

            // Act
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _service.UpdateAsync(baseProjectId, baseProjectEditRequest));

            // Assert
            var issues = JsonSerializer.Deserialize<List<ProductGroupIssue>>(exception.Message);

            issues.ShouldNotBeNull();
            issues.Should().Contain(i => i.ProductGroupId == 200);

            var issue = issues!.First(i => i.ProductGroupId == 200);
            issue.HasDataLoaded.ShouldBeTrue();
            issue.ProductGroupDesc.Should().Be("PG 200");
            issue.RbProjectIds.ShouldContain(999);
        }
        [Fact]
        public async Task GetUsersList_When_RepositoryReturnsNull_Expect_EmptyList()
        {
            // Arrange
            var expectedResult = new List<string>();
            _baseProjectRepositoryStub
                .Setup(r => r.GetUsersList())
                .ReturnsAsync((List<string>?)null);

            // Act
            var result = await _service.GetUsersList();

            // Assert
            result.Should().BeNull();
        }



    }
}

