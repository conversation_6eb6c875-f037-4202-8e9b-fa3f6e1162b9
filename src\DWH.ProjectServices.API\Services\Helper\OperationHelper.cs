﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Newtonsoft.Json;
using System.Globalization;
using System.Text;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;

namespace DWH.ProjectServices.API.Services.Helper
{
    public class OperationHelper : IOperationHelper
    {
        
        private readonly IQCPeriodRepository _qcPeriodRepository;
        private readonly ILogger<OperationHelper> _logger;
        private readonly ITokenService _tokenService;
        private readonly IBaseProjectRepository _baseProjectRepository;
        private readonly IQCProjectRepository _qcProjectRepository;
        private readonly IDateApiClient _dateApiClient;
        private readonly IAdministratorAPIClient _adminApiClient;
        
        public OperationHelper(IQCPeriodRepository qcPeriodRepository, ILogger<OperationHelper> logger
            , ITokenService tokenService, IBaseProjectRepository baseProjectRepository,
            IQCProjectRepository qcProjectRepository, IDateApiClient dateApiClient,
           IAdministratorAPIClient adminApiClient)
        {
            _qcPeriodRepository = qcPeriodRepository;
            _logger = logger;
            _tokenService = tokenService;
            _baseProjectRepository = baseProjectRepository;
            _qcProjectRepository = qcProjectRepository;
            _adminApiClient = adminApiClient;
            _dateApiClient = dateApiClient;
        }

        public async Task<List<PeriodDistance>> CalculateDistancesAsync(IEnumerable<Period> periods, long periodId)
        {
            var distances = new List<PeriodDistance>(Enumerable.Repeat(new PeriodDistance(0, null), 7));

            foreach (var period in periods)
            {
                var response = await _dateApiClient.GetAsync<List<DistanceResponse>>($"/api/v1/Periods/{periodId}/distances?offsetPeriods={period.RefPeriodId}");

                if (response.IsSuccess)
                {
                    var distanceResponses = response.Data;

                    if (distanceResponses != null && distanceResponses.Any())
                    {
                        var distanceValue = distanceResponses.First().distance;
                        distances[period.index] = new PeriodDistance(period.index, distanceValue);
                    }
                    else
                    {
                        var errorMessage = $"Failed to parse distance value for RefPeriodId {period.RefPeriodId}";
                        _logger.LogError(errorMessage);
                        throw new ArgumentNullException(errorMessage);
                    }
                }
                else
                {
                    var errorMessage = $"HTTP request failed: {response.StatusCode} ({response.ReasonPhrase}) - {response.ErrorMessage}";
                    _logger.LogError(errorMessage);
                    throw new HttpRequestException(errorMessage);
                }
            }

            return distances;
        }

        public async Task<QCPeriod> GetRecentQCPeriodsAsync(int qcProjectId)
        {
            var qcPeriods = await _qcPeriodRepository.GetAllQCPeriodsAsync(qcProjectId);

            if (!qcPeriods.Any())
            {
                var errorMessage = $"No QCPeriods exist with Id {qcProjectId}";
                _logger.LogError(errorMessage);
                throw new EntityNotExistsException(errorMessage);
            }
            return qcPeriods.First();


        }

        public async Task<List<ShiftedPeriod>> CalculateShiftedPeriodsAsync(long targetPeriodId, List<PeriodDistance> distances)
        {
            var filteredDistancesQuery = string.Join(",", distances
                .Where(d => d.Distance.HasValue)
                .Select(d => d.Distance.Value.ToString()));
      
            var response = await _dateApiClient.GetAsync<List<ShiftedPeriodResponse>>($"/api/v1/Periods/{targetPeriodId}/shifted?distances={filteredDistancesQuery}");
  
            if (response.IsSuccess)
            {
                var shiftedPeriods = response.Data;
                var shiftedPeriodMappings = distances.Select(d => new ShiftedPeriod(d.Index, null)).ToList();

                var filteredDistances = distances.Where(d => d.Distance.HasValue).ToList();

                for (int i = 0; i < filteredDistances.Count; i++)
                {
                    shiftedPeriodMappings[filteredDistances[i].Index] = new ShiftedPeriod(filteredDistances[i].Index, shiftedPeriods.ElementAtOrDefault(i)?.Id);
                }

                return shiftedPeriodMappings;
            }
            else
            {
                var errorMessage = $"Error calling API for shifted periods: {response.StatusCode} ({response.ReasonPhrase}) - {response.ErrorMessage}";
                _logger.LogError(errorMessage);
                throw new HttpRequestException(errorMessage);
            }
        }

        public async Task<QCPeriod> GetBaseQCPeriodAsync(int qcProjectId, long periodId)
        {
            var qcPeriods = await _qcPeriodRepository.GetAllQCPeriodsAsync(qcProjectId);

            if (!qcPeriods.Any())
            {
                var errorMessage = $"No QCPeriods exist with Id {qcProjectId}";
                _logger.LogError(errorMessage);
                throw new EntityNotExistsException(errorMessage);
            }

            var specificQCPeriod = qcPeriods.FirstOrDefault(qcp => qcp.PeriodId == periodId);
            if (specificQCPeriod == null)
            {
                var errorMessage = $"No QCPeriod exists with QCProjectId {qcProjectId} and PeriodId {periodId}";
                _logger.LogError(errorMessage);
                throw new EntityNotExistsException(errorMessage);
            }

            return specificQCPeriod;
        }


        public async Task<string> GetPeriodShortNameAsync(long PeriodId)
        {
       
            var response = await _dateApiClient.GetAsync<ShiftedPeriodResponse>($"/api/v1/Periods/{PeriodId}");
            if (response.IsSuccess)
            {
                return response.Data.ShortName;

            }
            else
            {
                throw new HttpRequestException($"Error calling API for current periods: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");

            }
        }


        public async Task<List<long>> GetPeriodRangeAsync(int PeriodicityId, string StartPeriod, string EndPeriod)
        {
      

            var response = await _dateApiClient.GetAsync<List<ShiftedPeriodResponse>>($"/api/v1/Periods?periodicityId={PeriodicityId}&from={StartPeriod}&to={EndPeriod}&limit={100}");

            if (response.IsSuccess)
            {
                var result = response.Data;
                List<long> ids = result.Select(spr => spr.Id).ToList();
                return ids;
            }
            else
            {
                throw new HttpRequestException($"Error calling API for current periods: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");

            }
        }

        public async Task<int> GetPeriodicityIdAsync(int qcProjectId)
        {
     
            var result = await _baseProjectRepository.GetBaseProjectAsync(qcProjectId);      
            return result.PeriodicityId;
        }

        public async Task<string> GetPeriodNameAsync(long PeriodId)
        {
            var response = await _dateApiClient.GetAsync<ShiftedPeriodResponse>($"/api/v1/Periods/{PeriodId}");

            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API for current periods: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }
            return response.Data.Name;
        }


        public async Task<string> ExtractAndFormatEndDateAsync(string name)
        {
            return await Task.Run(() =>
            {
          
                int startIndex = name.LastIndexOf('-') + 2; 
                int endIndex = name.LastIndexOf(')');

            
                string endDateString = name.Substring(startIndex, endIndex - startIndex);

                return ParseAndFormatDateAsync(endDateString);
            });
        }

        public async Task<string> ParseAndFormatDateAsync(string dateString)
        {
            return await Task.Run(() =>
            {
                
                DateTime endDate;
                if (DateTime.TryParseExact(dateString, "dd.MM.yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out endDate))
                {
                    // Format the DateTime object into the desired format
                    return endDate.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new FormatException("The date format is incorrect.");
                }
            });
        }


        public  async Task<string> ExtractAndFormatEndDateAsyncMonthy(string name)
        {
            return await Task.Run(async () =>
            {
                string endDateString;

                if (name.Contains('-') && name.Contains('('))
                {
                   
                    int startIndex = name.LastIndexOf('-') + 2; 
                    int endIndex = name.LastIndexOf(')');

                    if (startIndex < 0 || endIndex < 0 || startIndex >= name.Length || endIndex <= startIndex)
                    {
                        throw new ArgumentException("Invalid date format in name.");
                    }

                    endDateString = name.Substring(startIndex, endIndex - startIndex);
                }
                else if (name.Contains('-'))
                {
          
                    int startIndex = name.LastIndexOf('-') + 2; 
                    string endPart = name.Substring(startIndex).Trim();

                    if (!DateTime.TryParseExact(endPart, "MMMM yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime endDate))
                    {
                        throw new FormatException("The date format is incorrect.");
                    }

                 
                    endDate = new DateTime(endDate.Year, endDate.Month, DateTime.DaysInMonth(endDate.Year, endDate.Month));
                    endDateString = endDate.ToString("dd.MM.yyyy");
                }
                else
                {
                    throw new ArgumentException("Unrecognized date format in name.");
                }

                return await ParseAndFormatDateAsyncMonthly(endDateString);
            });
        }

        public  async Task<string> ParseAndFormatDateAsyncMonthly(string dateString)
        {
            return await Task.Run(() =>
            {
           
                DateTime endDate;
                if (DateTime.TryParseExact(dateString, "dd.MM.yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out endDate))
                {
                    
                    return endDate.ToString("yyyy-MM-dd");
                }
                else
                {
                    throw new FormatException("The date format is incorrect.");
                }
            });
        }


        public async Task<List<ShiftedPeriodResponse>> GetPeriodsAsyncForBulkQC(int periodicityId)
        {
            var response = await _dateApiClient.GetAsync<List<ShiftedPeriodResponse>>($"/api/v1/Periods?periodicityId={periodicityId}&limit=5000");


            if (response.IsSuccess)
            {
                return response.Data;
            }
            else
            {
                throw new HttpRequestException(
                    $"Error calling API for periods: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})"
                );
            }
        }

        public async Task<List<ShiftedPeriodResponse>> GetPeriodsAsync(int periodicityId)
        {
            var response = await _dateApiClient.GetAsync<List<ShiftedPeriodResponse>>($"/api/v1/Periods/current?periodicityId={periodicityId}");


            if (response.IsSuccess)
            {
                return response.Data;
            }
            else
            {
                throw new HttpRequestException(
                    $"Error calling API for periods: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})"
                );
            }
        }



        public async Task<BaseChannelRearrangements> GetBCRDetails(string endpoint, int[] productGroups)
        {
            var requestPayload = new
            {
                productGroupIds = productGroups
            };

            var requestContent = new StringContent(JsonConvert.SerializeObject(requestPayload), Encoding.UTF8, "application/json");

            var response = await _adminApiClient.PostAsync<BaseChannelRearrangements>(endpoint, requestContent);
            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API for current productGroups: Status Code {(int)response.StatusCode} ({response.ReasonPhrase}) - NumberOfConflicts: {response.Data.NumberOfConflicts}");
            }
            return response.Data;
        }

        public async Task<List<ShiftedPeriod>> GetShiftedPeriodsForProjectAsync(List<Period> baseRefPeriods, int offset)
        {
            var result = new List<ShiftedPeriod>();

            foreach (var period in baseRefPeriods)
            {
                try
                {
                    int refProjectId = (int)period.RefProjectId;
                    var currentRefPeriodId = period.RefPeriodId;
                    var index = period.index;

                    var periodicityId = await _baseProjectRepository.GetPeriodicityIdAsync(refProjectId);
                    
                    var allPeriods = await GetPeriodsAsyncForBulkQC(periodicityId);
                    var sorted = allPeriods.OrderBy(p => p.Id).ToList();

                    if (!sorted.Any(p => p.Id == currentRefPeriodId))
                    {
                        _logger.LogWarning($"RefPeriodId {currentRefPeriodId} not found in periodicity={periodicityId}. Injecting fallback.");
                       
                    }


                    var currentIndex = sorted.FindIndex(p => p.Id == currentRefPeriodId);
                   
                    if (currentIndex - offset < 0)
                    {
                        result.Add(new ShiftedPeriod(index, null));
                        continue;
                    }

                    var shifted = sorted[currentIndex - offset].Id;
                    result.Add(new ShiftedPeriod(index, shifted));
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Failed to shift RefPeriod for index {period.index}. Reason: {ex.Message}");
                    result.Add(new ShiftedPeriod(period.index, null));
                }
            }

            return result;
        }

    }
}