﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using System.Net.Http;
using Newtonsoft.Json;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.Services.Helper.Interface;
using System.Net;
using Microsoft.AspNetCore.Http.HttpResults;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using static DWH.ProjectServices.API.Domain.Models.BulkQCResponses;
using Microsoft.AspNetCore.Http.HttpResults;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;
using System.Net;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;

namespace DWH.ProjectServices.API.Services
{
    public class QCPeriodService : IQCPeriodService
    {
        private readonly IMapper _mapper;
        private readonly IQCPeriodRepository _qcPeriodRepository;
        private IOperationHelper  _operationHelper;
        private readonly IBaseProjectRepository _baseProjectRepository;
        private readonly IOutBoxItemRepository _outBoxItemRepository;

        public QCPeriodService(IMapper mapper, IQCPeriodRepository qcPeriodRepository, IOutBoxItemRepository outBoxItemRepository, IOperationHelper periodicityOperationHelper, IBaseProjectRepository baseProjectRepository)
        {
            _qcPeriodRepository = qcPeriodRepository;
            _mapper = mapper;
            _operationHelper = periodicityOperationHelper;
            _baseProjectRepository = baseProjectRepository;
            _outBoxItemRepository = outBoxItemRepository;
        }

        public async Task<QCPeriod> AddAsyncQCPeriod(QCPeriod qcPeriod)
        {
            var existingPeriods = await _qcPeriodRepository.CheckIfQCPeriodExists(qcPeriod.QCProjectId, qcPeriod.PeriodId);
            if (existingPeriods)
            {
                throw new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists for this QCProjectId.");
            }

            var result = await _qcPeriodRepository.AddAsyncQCPeriod(qcPeriod);

            if (result != null)
            {
                await _outBoxItemRepository.SaveMessagesAsync(
                    new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.Id },
                    ProjectMessageType.QCPeriodCreate);
            }
            return result;
        }

        public async Task<QCPeriod> EditPeriodAsync(long QCPeriodId, QCPeriodEdits qcPeriodEdit)
        {

            var qcPeriod = await _qcPeriodRepository.GetQCPeriodAsync(Convert.ToInt64(QCPeriodId));
            //QC Status Check work
            var qcStatusResponse = await CheckQCStatus(qcPeriod.BaseProjectId, qcPeriod.PeriodId);
            if (!qcStatusResponse.CanEditandDelete)
            {
                throw new InvalidOperationException("QC Period cannot be edited as it is currently in QC Status");
            }

            var result = await _qcPeriodRepository.EditPeriodAsync(QCPeriodId, qcPeriodEdit);
            var SyncId = qcPeriod.Id;
            var QcPeriodId = Convert.ToInt32(SyncId);

            await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = SyncId },
            ProjectMessageType.QCPeriodUpdate);
            return result;
        }

        public async Task<IEnumerable<QCPeriod>> GetAllQCPeriodsAsync(int qcProjectId)
        {
            return await _qcPeriodRepository.GetAllQCPeriodsAsync(qcProjectId);
        }

        public async Task<QCPeriodWithBPIdResponse> GetQCPeriodAsync(long qcPeriodId)
        {
            var qcPeriods = await _qcPeriodRepository.GetQCPeriodAsync(qcPeriodId);
            return qcPeriods;
        }

        public async Task<IReadOnlyList<ResponseInfoQCPeriod>> DeletePeriodAsync(QCPeriodDeletes qcPeriodDelete)
        {
            var idsToDelete = qcPeriodDelete.Ids;
            var responses = new List<ResponseInfoQCPeriod>();

            try
            {
                var firstQCPeriod = await _qcPeriodRepository.GetQCPeriodAsync(idsToDelete.First());
                var qcProjectId = firstQCPeriod.QCProjectId;

                var allQCPeriods = await GetAllQCPeriodsAsync(qcProjectId);

                if (allQCPeriods.Count() == 1)
                {
                    var firstPeriodId = idsToDelete.First();
                    responses.Add(new ResponseInfoQCPeriod(firstPeriodId.ToString(), (int)HttpStatusCode.BadRequest,
                        $"Operation not allowed: Deleting QCPeriod {firstPeriodId} would leave QCProject {qcProjectId} without any linked periods."));
                    return responses;
                }

                if (idsToDelete.Count == allQCPeriods.Count())
                {
                    var idsToActuallyDelete = idsToDelete.Take(idsToDelete.Count - 1).ToList();
                    var deleteRequest = new QCPeriodDeletes { Ids = idsToActuallyDelete };

                    responses = await DeleteQCPeriodAsync(idsToDelete, deleteRequest);

                    var lastPeriodId = idsToDelete.Last();
                    responses.Add(new ResponseInfoQCPeriod(lastPeriodId.ToString(), (int)HttpStatusCode.BadRequest,
                        $"Operation not allowed: Deleting QCPeriod {lastPeriodId} would leave QCProject {qcProjectId} without any linked periods."));
                }
                else
                {
                    responses = await DeleteQCPeriodAsync(idsToDelete, qcPeriodDelete);
                }
            }
            catch (InvalidOperationException ex)
            {
                throw;
            }
            return responses;
        }

        private async Task<List<ResponseInfoQCPeriod>> DeleteQCPeriodAsync(List<long> idsToDelete, QCPeriodDeletes qcPeriodDeletes)
        {
            var deleteSyncIds = new List<string>();
            var responses = new List<ResponseInfoQCPeriod>();
            var qcPeriodIds = new List<long>();

            foreach (var id in qcPeriodDeletes.Ids)
            {
                var qcPeriod = await _qcPeriodRepository.GetQCPeriodAsync(Convert.ToInt64(id));
                var SyncId = $"{qcPeriod.QCProjectId}-{qcPeriod.PeriodId}";

                //QC Status Check work
                var qcStatusResponse = await CheckQCStatus(qcPeriod.BaseProjectId, qcPeriod.PeriodId);
                if (qcStatusResponse.CanEditandDelete)
                {
                    qcPeriodIds.Add(qcPeriod.Id);
                    deleteSyncIds.Add(SyncId);
                }
                else
                {

                    responses.Add(new ResponseInfoQCPeriod(
                        id.ToString(),
                        (int)HttpStatusCode.BadRequest,
                        $"QC Period {id} cannot be deleted as it is currently in QC"));
                }
            }

            if (qcPeriodIds.Count > 0)
            {
                qcPeriodDeletes.Ids = qcPeriodIds;
            }
            else
            {
                throw new InvalidOperationException("QC Period(s) cannot be deleted as they are currently in QC Status");
            }

            var result = await _qcPeriodRepository.DeletePeriodAsync(qcPeriodDeletes);

            var successfulResponses = result?.Where(item => item.StatusCode == StatusCodes.Status200OK)?.ToList();

            if (successfulResponses.Any())
            {

                foreach (var QcPeriodId in deleteSyncIds)
                {
                    await _outBoxItemRepository.SaveMessagesAsync(
                    new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = QcPeriodId },
                    ProjectMessageType.QCPeriodDelete);
                }
            }
            responses.AddRange(result);
            return responses;
        }

        private async Task<(bool CanEditandDelete, List<int> QCStatusBps)> CheckQCStatus(int baseProjectId, long periodId)
        {
            QCStatuses qcStatuses = new QCStatuses
            {
                BaseProjectIds = new List<int> { baseProjectId },
                ReportingPeriodIds = new List<long> { periodId }
            };

            var qcStatusBps = await _baseProjectRepository.GetBaseProjectWithQCStatus(qcStatuses);
            return (qcStatusBps.Count == 0, qcStatusBps);
        }


        public async Task<AutoQCResponses> AutoQCPeriodCreateAsync(long targetPeriodId, AutoQCPeriods autoQCPeriodRequest, string userName)
        {
            var createdQCPeriods = new List<AutoQCPeriodRecord>();

            foreach (var qcProjectId in autoQCPeriodRequest.QCProjectIds)
            {
                try
                {
                    var baseQCPeriod = await _operationHelper.GetRecentQCPeriodsAsync(qcProjectId);

                    if (baseQCPeriod?.Periods == null || !baseQCPeriod.Periods.Any())
                    {
                        var newQCPeriodWithoutReferences = new QCPeriod
                        {
                            QCProjectId = qcProjectId,
                            CreatedBy = userName ?? "",
                            PeriodId = targetPeriodId,
                            Periods = new List<Period>(),
                            StockInitialization = new StockInitialization
                            {
                                QCPeriodId = qcProjectId,
                                StockBaseProjectId = null,
                                StockPeriodId = null
                            }
                        };

                        var responseQCPeriod = await AddAsyncQCPeriod(newQCPeriodWithoutReferences);
                        createdQCPeriods.Add(new AutoQCPeriodRecord
                        {
                            QCProjectId = qcProjectId,
                            QCPeriodId = responseQCPeriod.Id,
                            Period = responseQCPeriod.PeriodId,
                            StatusCode = (int)HttpStatusCode.Created,
                            StatusMsg = "QC period created successfully without reference periods"
                        });

                        continue;
                    }

                    var distances = await _operationHelper.CalculateDistancesAsync(baseQCPeriod.Periods, baseQCPeriod.PeriodId);

                    var shiftedPeriods = await _operationHelper.CalculateShiftedPeriodsAsync(targetPeriodId, distances);

                    var newQCPeriod = new QCPeriod
                    {
                        QCProjectId = qcProjectId,
                        CreatedBy = userName ?? "",
                        PeriodId = targetPeriodId,
                        Periods = shiftedPeriods
                            .Where(sp => sp.ShiftedPeriodId.HasValue)
                            .Select(sp =>
                            {
                                var basePeriod = baseQCPeriod.Periods.FirstOrDefault(p => p.index == sp.Index);
                                return basePeriod == null ? null : new Period
                                {
                                    QCPeriodId = targetPeriodId,
                                    RefPeriodId = sp.ShiftedPeriodId,
                                    RefProjectId = basePeriod.RefProjectId ?? 0,
                                    index = basePeriod.index
                                };
                            })
                            .Where(p => p != null)
                            .ToList(),
                        StockInitialization = new StockInitialization
                        {
                            QCPeriodId = qcProjectId,
                            StockBaseProjectId = null,
                            StockPeriodId = null
                        }
                    };

                    var responseWithReferences = await AddAsyncQCPeriod(newQCPeriod);
                    createdQCPeriods.Add(new AutoQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        QCPeriodId = responseWithReferences.Id,
                        Period = responseWithReferences.PeriodId,
                        StatusCode = (int)HttpStatusCode.Created,
                        StatusMsg = "QC period created successfully"
                    });
                }
                catch (EntityNotExistsException ex)
                {
                    createdQCPeriods.Add(new AutoQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        StatusCode = (int)HttpStatusCode.NotFound,
                        StatusMsg = ex.Message
                    });
                }
                catch (EntityAlreadyExistsException ex)
                {
                    createdQCPeriods.Add(new AutoQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        StatusCode = (int)HttpStatusCode.BadRequest,
                        StatusMsg = "DUPLICATION_ENTITY"
                    });
                }
                catch (HttpRequestException ex)
                {
                    createdQCPeriods.Add(new AutoQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        StatusCode = (int)HttpStatusCode.InternalServerError,
                        StatusMsg = ex.Message
                    });
                }
                catch (Exception ex)
                {
                    createdQCPeriods.Add(new AutoQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        StatusCode = (int)HttpStatusCode.InternalServerError,
                        StatusMsg = $"Unexpected error: {ex.Message}"
                    });
                }
            }

            return new AutoQCResponses
            {
                AutoQCPeriods = createdQCPeriods
            };
        }

        public async Task<BulkQCResponses> CreateBulkQCPeriodAsync(int qcProjectId, BulkQCPeriods bulkQCPeriodRange, string userName)
        {
            var createdQCPeriods = new List<BulkQCPeriodRecord>();
            var qcPeriodList = new List<long>();

            try
            {
                var baseQCPeriod = await _operationHelper.GetBaseQCPeriodAsync(qcProjectId, bulkQCPeriodRange.EndPeriod);

                string startPeriodName = await _operationHelper.GetPeriodShortNameAsync(bulkQCPeriodRange.StartPeriod);
                string endPeriodName = await _operationHelper.GetPeriodShortNameAsync(bulkQCPeriodRange.EndPeriod);

                int periodicityId = await _operationHelper.GetPeriodicityIdAsync(qcProjectId);

                (string startDate, string endDate) = await SetStartEndDateAsync(
                    periodicityId,
                    bulkQCPeriodRange.StartPeriod,
                    bulkQCPeriodRange.EndPeriod,
                    startPeriodName,
                    endPeriodName
                );

                qcPeriodList = await _operationHelper.GetPeriodRangeAsync(periodicityId, startDate, endDate);
                qcPeriodList.Add(bulkQCPeriodRange.StartPeriod);

                var existingQCPeriods = await _qcPeriodRepository.GetAllQCPeriodsAsync(qcProjectId);

                int offset = 1;
                foreach (var qcPeriodId in qcPeriodList)
                {
                    var result = await CreateOrSkipQCPeriodAsync(qcProjectId, qcPeriodId, baseQCPeriod, userName, existingQCPeriods.ToList(), offset);
                    createdQCPeriods.Add(result);
                    offset++;
                }
            }
            catch (Exception ex)
            {
                createdQCPeriods.Add(CreateExceptionRecord(ex, qcProjectId, null));
            }

            return new BulkQCResponses
            {
                BulkQCPeriods = createdQCPeriods
            };
        }

        private async Task<(string startDate, string endDate)> SetStartEndDateAsync(int periodicityId, long startPeriodId, long endPeriodId, string startPeriodName, string endPeriodName)
        {
            string startDate = string.Empty;
            string endDate = string.Empty;

            if (periodicityId == (int)PeriodicityType.Weekly || periodicityId == (int)PeriodicityType.Daily)
            {
                var periodNameStart = await _operationHelper.GetPeriodNameAsync(startPeriodId);
                var periodEndName = await _operationHelper.GetPeriodNameAsync(endPeriodId);
                
                if (periodicityId == (int)PeriodicityType.Weekly)
                {
                    startDate = await _operationHelper.ExtractAndFormatEndDateAsync(periodNameStart);
                    endDate = await _operationHelper.ExtractAndFormatEndDateAsync(periodEndName);
                }
                else
                {
                    startDate = await _operationHelper.ParseAndFormatDateAsync(periodNameStart);
                    endDate = await _operationHelper.ParseAndFormatDateAsync(periodEndName);
                }
            }

            else if (periodicityId == (int)PeriodicityType.Monthly)
            {
                startDate = ConvertPeriodNameToDate(startPeriodName, isStartDate: true);
                endDate = ConvertPeriodNameToDate(endPeriodName, isStartDate: false);
            }
            else
            {
                var periodNameStart = await _operationHelper.GetPeriodNameAsync(startPeriodId);
                var periodEndName = await _operationHelper.GetPeriodNameAsync(endPeriodId);
                startDate = await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(periodNameStart);
                endDate = await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(periodEndName);
            }

            return (startDate, endDate);
        }


        public async Task<List<long>> GetAuthorizedCountriesForPeriods(QCPeriodCountries qcPeriodCountryRequest)
        {
            var result = await _qcPeriodRepository.GetAuthorizedCountriesForPeriods(qcPeriodCountryRequest);
            return result;
        }

        private string ConvertPeriodNameToDate(string periodName, bool isStartDate)
        {
            if (string.IsNullOrWhiteSpace(periodName) || periodName.Length < 5)
                return periodName; // Return as-is if format is incorrect

            var monthMap = new Dictionary<string, int>
            {
                { "Jan", 1 }, { "Feb", 2 }, { "Mar", 3 }, { "Apr", 4 },
                { "May", 5 }, { "Jun", 6 }, { "Jul", 7 }, { "Aug", 8 },
                { "Sep", 9 }, { "Oct", 10 }, { "Nov", 11 }, { "Dec", 12 }
            };

            string monthPart = periodName.Substring(0, 3);
            string yearPart = periodName.Substring(3);

            if (monthMap.TryGetValue(monthPart, out int month) && int.TryParse($"20{yearPart}", out int year))
            {
                int day = isStartDate ? 1 : 2; // Apr23 → 2023-04-01, Mar25 → 2025-03-02
                return new DateTime(year, month, day).ToString("yyyy-MM-dd");
            }

            return periodName; // Return original if parsing fails
        }

        private async Task<BulkQCPeriodRecord> CreateOrSkipQCPeriodAsync(int qcProjectId, long qcPeriodId,QCPeriod baseQCPeriod, string userName, List<QCPeriod> existingQCPeriods, int offset)
        {
            try
            {
                var existingQCPeriod = existingQCPeriods.FirstOrDefault(p => p.PeriodId == qcPeriodId);
                if (existingQCPeriod != null)
                {
                    return new BulkQCPeriodRecord
                    {
                        QCProjectId = qcProjectId,
                        QCPeriodId = qcPeriodId,
                        StatusCode = (int)HttpStatusCode.OK,
                        StatusMsg = $"QC Period {qcPeriodId} already exists, skipping creation."
                    };
                }

                var shiftedPeriods = await _operationHelper.GetShiftedPeriodsForProjectAsync(baseQCPeriod.Periods.ToList(), offset);

                var newQCPeriod = new QCPeriod
                {
                    QCProjectId = qcProjectId,
                    CreatedBy = userName ?? string.Empty,
                    PeriodId = qcPeriodId,
                    Periods = shiftedPeriods
                        .Where(p => p.ShiftedPeriodId.HasValue)
                        .Select(sp =>
                        {
                            var basePeriod = baseQCPeriod.Periods.FirstOrDefault(p => p.index == sp.Index);
                            return basePeriod == null ? null : new Period
                            {
                                QCPeriodId = qcPeriodId,
                                RefPeriodId = sp.ShiftedPeriodId,
                                RefProjectId = basePeriod.RefProjectId ?? 0,
                                index = basePeriod.index
                            };
                        })
                        .Where(p => p != null)
                        .ToList(),
                    StockInitialization = new StockInitialization
                    {
                        QCPeriodId = qcProjectId,
                        StockBaseProjectId = null,
                        StockPeriodId = null
                    }
                };

                var responseQCPeriod = await _qcPeriodRepository.AddAsyncQCPeriod(newQCPeriod);
                await _outBoxItemRepository.SaveMessagesAsync(
                new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = responseQCPeriod.Id },
                 ProjectMessageType.QCPeriodCreate);

                return new BulkQCPeriodRecord
                {
                    QCProjectId = qcProjectId,
                    QCPeriodId = responseQCPeriod.Id,
                    Period = responseQCPeriod.PeriodId,
                    StatusCode = (int)HttpStatusCode.Created,
                    StatusMsg = "QC periods created successfully"
                };
            }
            catch (Exception ex)
            {
                return CreateExceptionRecord(ex, qcProjectId, qcPeriodId);
            }
        }


        private BulkQCPeriodRecord CreateExceptionRecord(Exception ex, int qcProjectId, long? qcPeriodId)
        {
            var record = new BulkQCPeriodRecord
            {
                QCProjectId = qcProjectId,
                QCPeriodId = qcPeriodId ?? 0,
            };

            switch (ex)
            {
                case EntityNotExistsException _:
                    record.StatusCode = (int)HttpStatusCode.NotFound;
                    record.StatusMsg = ex.Message;
                    break;

                case EntityAlreadyExistsException _:
                    record.StatusCode = (int)HttpStatusCode.BadRequest;
                    record.StatusMsg = ex.Message;
                    break;

                case HttpRequestException _:
                    record.StatusCode = (int)HttpStatusCode.InternalServerError;
                    record.StatusMsg = ex.Message;
                    break;

                default:
                    record.StatusCode = (int)HttpStatusCode.InternalServerError;
                    record.StatusMsg = $"Unexpected error: {ex.Message}";
                    break;
            }

            return record;
        }


    }
}
