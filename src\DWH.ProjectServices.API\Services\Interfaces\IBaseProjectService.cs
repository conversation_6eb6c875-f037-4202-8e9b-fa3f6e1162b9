﻿
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using Microsoft.AspNetCore.Mvc;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Services.Interfaces
{
    public interface IBaseProjectService
    {

        Task<BaseProject> AddAsync(BaseProject baseProject);
        Task<IEnumerable<BaseProject>> GetAllAsync(BaseProjectPredecessor baseProjectPredecessor);
        Task<BaseProject> UpdateAsync(int baseProjectId, BaseProject baseProject);
        Task<BaseProject> GetAsync(int baseProjectId);
        Task<BaseProjectLists> GetAsyncList(BaseProjectsLists baseProjectLists);

        Task<IReadOnlyList<Dependencies>> DeleteAsync(BaseProjectDeletes baseProjectDelete);
        Task<ProjectLists> GetAsyncListBaseProjects(BaseProjectNameandIdLists baseProjectsLists);

        Task<List<int>> GetAsync(BaseProjectCountries baseProjectCountryRequest);
        Task<IReadOnlyList<string>> GetUsersList();
        Task<IReadOnlyList<Dependencies>> AddBulkAsync(List<int> baseProjectIds, string userName);

    }
}
