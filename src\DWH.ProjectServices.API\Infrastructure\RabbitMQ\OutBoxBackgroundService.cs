﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class OutBoxBackgroundService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILogger<OutBoxBackgroundService> _logger;
        private readonly IRabbitMQSender _rabbitMqSender;

        public OutBoxBackgroundService(IServiceScopeFactory serviceScopeFactory, ILogger<OutBoxBackgroundService> logger, IRabbitMQSender rabbitMqSender)
        {
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
            _rabbitMqSender = rabbitMqSender;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using (var scope = _serviceScopeFactory.CreateScope())
                    {
                        var repository = scope.ServiceProvider.GetRequiredService<IOutBoxItemRepository>();
                        var messages = await repository.GetUnprocessedMessagesAsync();
                        foreach (var message in messages)
                        {
                           
                            try
                            {
                                var (exchange, routingKey) = GetRabbitMQSettings(message.TypeId);

                                if (message.TypeId == ProjectMessageType.RetailerSeparationRequest.ToString())
                                {
                                    var payload = JsonSerializer.Deserialize<RetailerSeparationsData>(message.Payload);
                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }
                                else if (message.TypeId == ProjectMessageType.RetailerSeparationSecurityRequest.ToString())
                                {
                                    var payload = JsonSerializer.Deserialize<RetailerSeparationsSecurityData>(message.Payload);
                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }
                                else
                                {
                                    var payload = JsonSerializer.Deserialize<ProjectServicesData>(message.Payload);

                                    await _rabbitMqSender.SendToRabbitMQ(exchange, routingKey, payload);

                                    await repository.MarkMessageAsProcessedAsync(message.Id);
                                }

                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"Error processing message {message.Id}: {ex.Message}");
                            }
                        }
                    }
                    await Task.CompletedTask;
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);

                }
                catch (Exception ex)
                {
                    _logger.LogError($"Outbox Background Service error: {ex.Message}");
                }
            }

        }


        private (string Exchange, string RoutingKey) GetRabbitMQSettings(string typeId)
        {
            return typeId switch
            {
                "BaseProjectCreate" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.CreateRoutingKey),
                "BaseProjectUpdate" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.UpdateRoutingKey),
                "BaseProjectDelete" => (BaseProjectConstants.BaseProjectExchange, RMQConstants.DeleteRoutingKey),
                "QCProjectCreate" => (QCProjectConstants.QCProjectExchange, RMQConstants.CreateRoutingKey),
                "QCProjectUpdate" => (QCProjectConstants.QCProjectExchange, RMQConstants.UpdateRoutingKey),
                "QCProjectDelete" => (QCProjectConstants.QCProjectExchange, RMQConstants.DeleteRoutingKey),
                "QCPeriodCreate" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.CreateRoutingKey),
                "QCPeriodUpdate" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.UpdateRoutingKey),
                "QCPeriodDelete" => (QCPeriodConstants.QCPeriodExchange, RMQConstants.DeleteRoutingKey),
                "RetailerSeparationRequest" => (RetailerSeparationConstants.RetailerSeparationExchange, RMQConstants.RetailerSeparationProjectServicesRoutingKey),
                "RetailerSeparationSecurityRequest" => (ProjectSecurityConstants.ProjectServicesSecurityExchange, RMQConstants.ProjectServicesSecurityRoutingKey),
                _ => throw new ArgumentException($"Unknown TypeId: {typeId}")
            };
        }

    }
}
