﻿using System.Threading;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using Elastic.Apm.Api;

namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class RetailerSeparationConsumerService: BackgroundService
    {
        private readonly ILogger<RetailerSeparationConsumerService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public RetailerSeparationConsumerService(ILogger<RetailerSeparationConsumerService> logger,IServiceProvider service)
        {
            _logger = logger;
            _serviceProvider = service;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation(">>> Entered in ExecuteAsync <<<");
                var scope = _serviceProvider.CreateScope();
                var rabbitMQConsumer = scope.ServiceProvider.GetRequiredService<IMessageConsumer>();
                await rabbitMQConsumer.ConsumeAndSync();
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EXCEPTION in ExecuteAsync");
                await base.StopAsync(stoppingToken);
            }

        }

    }
}
