﻿using AutoMapper;
using Azure;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Interfaces;
using Elastic.Apm.Api;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using DWH.ProjectServices.API.Presentation.Profile;
using System.Linq;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.Presentation.Controllers
{
    public class QCPeriodsController : ApiController
    {
        private readonly IMapper _mapper;
        private readonly IQCPeriodService _qcPeriodService;
        private readonly IQCProjectService _qcProjectService;
        private IRabbitMQSender _rabbitMQsender;

        public QCPeriodsController(IMapper mapper, IQCPeriodService qcPeriodService, IQCProjectService qcProjectService, IRabbitMQSender rabbitMQsender)
        {
            _mapper = mapper;
            _qcPeriodService = qcPeriodService;
            _rabbitMQsender = rabbitMQsender;
            _qcProjectService = qcProjectService;

        }

        /// <summary>
        ///Create a new QCPeriod
        /// </summary>
        [HttpPost("qcPeriod")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(QCPeriodResponse), StatusCodes.Status201Created)]
        public async Task<IActionResult> AddAsync([FromHeader(Name = "Custom-Countryid")] string countryIds,
        [Required] [FromHeader(Name = "userName")]
        string userName, QCPeriodCreateRequest qcPeriodCreateRequest)
        {
            if (!string.IsNullOrEmpty(countryIds))
            {
                var qcProjectCountryModel = new QCProjectCountries();
                qcProjectCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToArray();
                qcProjectCountryModel.QCProjectIds = new int[] { qcPeriodCreateRequest.QCProjectId };
                var countryResult = await _qcProjectService.GetAsync(qcProjectCountryModel);
                if (countryResult.Count == 0)
                {
                    return StatusCode(StatusCodes.Status403Forbidden);
                }
            }
            qcPeriodCreateRequest.CreatedBy = userName;
            qcPeriodCreateRequest.UpdatedBy = userName;

            var qcPeriod = _mapper.Map<QCPeriod>(qcPeriodCreateRequest);
            var result = await _qcPeriodService.AddAsyncQCPeriod(qcPeriod);

            if (result != null)
            {
                var response = _mapper.Map<QCPeriodResponse>(result);
                return CreatedAtAction(null, new { qcPeriodId = response.Id }, response);
            }
            return BadRequest(result);
        }



        ///<summary>
        /// Edit a QCPeriod 
        /// </summary>
        [HttpPut("qcperiods/{qcperiodId}")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(QCPeriodEditResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UpdateAsyncQCPeriod([FromHeader(Name = "Custom-Countryid")] string countryIds,
        [Required] [FromHeader(Name = "userName")]
        string userName, long qcperiodId, QCPeriodEditRequest editQCPeriodRequest)
        {
            try
            {
                if (!string.IsNullOrEmpty(countryIds))
                {
                    var qcProjectCountryModel = new QCPeriodCountries();
                    qcProjectCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToArray();
                    qcProjectCountryModel.QCPeriodIds = new long[] { qcperiodId };
                    var countryResult = await _qcPeriodService.GetAuthorizedCountriesForPeriods(qcProjectCountryModel);
                    if (countryResult.Count == 0)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden);
                    }
                }
                editQCPeriodRequest.UpdatedBy = userName;
                var qcPeriodEditReq = _mapper.Map<QCPeriodEdits>(editQCPeriodRequest);
                var response = await _qcPeriodService.EditPeriodAsync(qcperiodId, qcPeriodEditReq);
                var result = _mapper.Map<QCPeriodEditResponse>(response);
                return OkOrEmpty(result);
            }

            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }



        /// <summary>
        /// Get all QCPeriods
        /// </summary>
        [HttpGet("qcperiods/{qcprojectId}")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(QCPeriodListResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllQCPeriods(int qcprojectId)
        {
            var response = await _qcPeriodService.GetAllQCPeriodsAsync(qcprojectId);
            var qcPeriodListResponse = new QCPeriodListResponse
            {
                QCProjectId = qcprojectId,
                QCPeriods = _mapper.Map<ICollection<QCPeriodModelDto>>(response)
            };

            qcPeriodListResponse.Counts = response.Count();
            qcPeriodListResponse.MoreRecordsAvailable = false;
            return Ok(qcPeriodListResponse);
        }


        ///<summary>
        /// Get QCPeriod 
        /// </summary>
        [HttpGet("qcperiod/{qcperiodId}")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(QCPeriodWithBPIdResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetQCPeriodAsync(long qcperiodId)
        {
            var result = await _qcPeriodService.GetQCPeriodAsync(qcperiodId);
            return Ok(result);
        }



        /// <summary>
        /// Delete QCPeriod
        /// </summary>
        [HttpDelete("qcperiods")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(QCPeriodEditResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteAsyncQCPeriod([FromHeader(Name = "Custom-Countryid")] string countryIds, QCPeriodDeleteRequest deleteQCPeriodRequest)
        {
            try
            {
                List<long> validQCPeriods = new List<long>();
                validQCPeriods = deleteQCPeriodRequest.Ids.ToList();
                if (!string.IsNullOrEmpty(countryIds))
                {
                    var qcProjectCountryModel = new QCPeriodCountries();
                    qcProjectCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToArray();
                    qcProjectCountryModel.QCPeriodIds = deleteQCPeriodRequest.Ids.ToArray();
                    var countryResult = await _qcPeriodService.GetAuthorizedCountriesForPeriods(qcProjectCountryModel);
                    if (countryResult.Count == 0)
                    {
                        return StatusCode(StatusCodes.Status403Forbidden);
                    }
                    else
                    {

                        validQCPeriods = countryResult;
                    }
                }
                deleteQCPeriodRequest.Ids = validQCPeriods;
                var qcPeriodDelete = _mapper.Map<QCPeriodDeletes>(deleteQCPeriodRequest);
                IReadOnlyList<ResponseInfoQCPeriod> responses = await _qcPeriodService.DeletePeriodAsync(qcPeriodDelete);
                return StatusCode(StatusCodes.Status207MultiStatus, responses);
            }
            catch(InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }


        [HttpPost("{targetPeriodId}/extend")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(AutoQCPeriodResponse), StatusCodes.Status207MultiStatus)]
        public async Task<IActionResult> CreateAsyncAutoQc(
    [FromHeader(Name = "userName")] string userName,
    AutoQCPeriodCreateRequest autoQCPeriodCreateRequest,
    long targetPeriodId)
        {
            var qcProjects = _mapper.Map<AutoQCPeriods>(autoQCPeriodCreateRequest);
            var result = await _qcPeriodService.AutoQCPeriodCreateAsync(targetPeriodId, qcProjects, userName);

            if (result != null)
            {
                var response = _mapper.Map<AutoQCPeriodResponse>(result);
                return StatusCode(StatusCodes.Status207MultiStatus, response);
            }
            else
            {
                var errorMessage = "Failed to create AutoQCPeriod. Please check your request and try again.";
                return BadRequest(new { error = errorMessage });
            }
        }



        [HttpPost("qcperiod/bulkQCPeriod/{qcProjectId}")]
        [Consumes("application/json")]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status403Forbidden)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(typeof(BulkQCPeriodResponse), StatusCodes.Status207MultiStatus)]
        public async Task<IActionResult> CreateAsyncBulkQc(
        [FromHeader(Name = "userName")] string userName,
        BulkQCPeriodRequest bulkQCPeriodRequest,
        int qcProjectId, [FromHeader(Name = "Custom-Countryid")] string countryIds)
        {
            if (!string.IsNullOrEmpty(countryIds))
            {
                var authorizedQCProject = await FilterQCProjectByCountry(countryIds, qcProjectId);
                if (authorizedQCProject == 0)
                 return StatusCode(StatusCodes.Status403Forbidden);
            }
            var periodRange = _mapper.Map<BulkQCPeriods>(bulkQCPeriodRequest);
            var result = await _qcPeriodService.CreateBulkQCPeriodAsync(qcProjectId, periodRange, userName);

            if (result != null)
            {
                var response = _mapper.Map<BulkQCPeriodResponse>(result);
                return StatusCode(StatusCodes.Status207MultiStatus, response);
            }
            else
            {
                var errorMessage = "Failed to create AutoQCPeriod. Please check your request and try again.";
                return BadRequest(new { error = errorMessage });
            }
        }


        private async Task<int?> FilterQCProjectByCountry(string countryIds, int qcProjectId)
        {

            var qcProjectCountryModel = new QCProjectCountryIds();
            qcProjectCountryModel.CountryIds = countryIds.Split(',').Select(int.Parse).ToArray();
            qcProjectCountryModel.QCProjectId = qcProjectId;

            var countryResult = await _qcProjectService.GetFilteredQCProjectAsync(qcProjectCountryModel);

            return countryResult;
        }
    }
}
