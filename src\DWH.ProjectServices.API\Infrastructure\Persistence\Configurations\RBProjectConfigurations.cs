﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class RBProjectConfigurations : IEntityTypeConfiguration<RBProject>
    {
        public void Configure(EntityTypeBuilder<RBProject> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_RBPROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("RBPROJECTID");
            builder.Property(p => p.Deleted).HasColumnName("DELETED");

        }
    }
}
