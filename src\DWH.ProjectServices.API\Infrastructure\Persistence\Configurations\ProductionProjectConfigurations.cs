﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class ProductionProjectConfigurations : IEntityTypeConfiguration<ProductionProject>
    {
        public void Configure(EntityTypeBuilder<ProductionProject> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_PRODPROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("PRODPROJECTID");
            builder.Property(p => p.Deleted).HasColumnName("DELETED");

        }
    }
}
