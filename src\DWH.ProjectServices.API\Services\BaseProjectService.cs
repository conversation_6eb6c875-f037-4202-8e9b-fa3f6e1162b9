﻿using AutoMapper;
using DWH.ProjectServices.API.Services.Interfaces;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using System.Net;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using Microsoft.AspNetCore.Server.IIS.Core;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.AspNetCore.Mvc;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;
using ProjectSubType = DWH.ProjectServices.API.Domain.Enum.ProjectSubType;
using DWH.ProjectServices.API.Services.Constants;
using Microsoft.Extensions.DependencyInjection;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using System.Text;
using System.Text.Json;

namespace DWH.ProjectServices.API.Services
{
    public class BaseProjectService : IBaseProjectService
    {
        private readonly IMapper _mapper;
        private readonly ILogger<BaseProjectService> _logger;
        private readonly IBaseProjectRepository _baseProjectRepository;
        private readonly IOutBoxItemRepository _outBoxItemRepository; 
        private IOperationHelper _operationHelper;
        private IRoleHelper _roleHelper;
        private ISecurityHelper _securityHelper;

        public BaseProjectService(IMapper mapper, IBaseProjectRepository baseProjectRepository, IOutBoxItemRepository outBoxItemRepository, IOperationHelper operationHelper, ILogger<BaseProjectService> logger, IRoleHelper roleHelper, ISecurityHelper securityHelper)
        {
            _baseProjectRepository = baseProjectRepository;
            _mapper = mapper;
            _outBoxItemRepository = outBoxItemRepository;
            _operationHelper = operationHelper;
            _logger = logger;
            _roleHelper = roleHelper;
            _securityHelper = securityHelper;
        }

        public async Task<BaseProject> AddAsync(BaseProject baseProject)
        {
            if (baseProject.PanelId != (int)PanelType.IDAS) // for distributor
            {
                baseProject.QCProjects = await CreateQCProjectModelAsync(baseProject);
            }
            else if (baseProject.PanelId == (int)PanelType.IDAS)
            {
                baseProject.QCProjects = null;
            }

            if (baseProject.ProductGroups.Count > 1 && baseProject.PanelId == (int)PanelType.POS)
            {
                var bcrData = CheckBaseChannelRearrangements(baseProject.ProductGroups);
                if (!bcrData.Result.Success)
                {
                    _logger.LogError($"BaseProjectService - AddAsync EXCEPTION :: BCR Conflicts found. Number of conflicts: {bcrData.Result.NumberOfConflicts}");
                    throw new BadHttpRequestException($"BCR Conflicts found. Number of conflicts {bcrData.Result.NumberOfConflicts}");
                }
            }

            var result = await _baseProjectRepository.AddAsync(baseProject);

            if (result != null)
            {
                await SendBPToOutBox(result, baseProject.PanelId);
                await HandleBPSecurityMasterUsers(result, baseProject.CreatedBy);
            }
            return result;
        }

        private async Task HandleBPSecurityMasterUsers(BaseProject baseProject, string createdBy)
        {
            // QC Security Master Users Add Work
            var masterUserIds = await _roleHelper.GetMasterUsers();
            if (masterUserIds.Count > 0)
            {
                AssignUsersToProjectDetails assignUsersToProjectDetails = new AssignUsersToProjectDetails
                {
                    ProjectTypeId = ProjectServicesSecurityConstants.BPSecurity,
                    UserIds = masterUserIds,
                    ProjectIds = new List<int> { baseProject.Id }
                };
                await _securityHelper.AssignUsersToProject(assignUsersToProjectDetails, createdBy);
            }
        }

        private async Task<BaseChannelRearrangements> CheckBaseChannelRearrangements(ICollection<BaseProjectProductGroup> productGroups)
        {
            int[] pgIds = productGroups.Select(pg => pg.ProductGroupId).ToArray();
            var result = await _operationHelper.GetBCRDetails("/v1/base-channel-rearrangement/check", pgIds);
            return result;
        }

        public async Task<IEnumerable<BaseProject>> GetAllAsync(BaseProjectPredecessor baseProjectPredecessor)
        {
            var result = await _baseProjectRepository.GetAllAsync(baseProjectPredecessor);

            if (!result.Any())
                throw new EntityNotExistsException($"No Base Project exists with Country Id {baseProjectPredecessor.CountryId}", "BaseProject", baseProjectPredecessor.CountryId);

            return result;
        }


        public async Task<BaseProject> UpdateAsync(int baseProjectId, BaseProject editBaseProject)
        {
            var baseProject = await _baseProjectRepository.GetAsync(baseProjectId);
            var previouspgs = _mapper.Map<BaseProjectGetResponse>(baseProject);

            var deletedProductGroups = previouspgs.ProductGroups
                .Where(pg => !editBaseProject.ProductGroups
                    .Any(updatedPg => updatedPg.ProductGroupId == pg.ProductGroupId))
                .Select(pg => pg.ProductGroupId)
                .ToList();

            var isPGDeleted = deletedProductGroups.Count > 0;

            await CheckDeletedProductGroupIssuesAsync(deletedProductGroups, baseProjectId);

            editBaseProject.QCProjects = baseProject.QCProjects;
            editBaseProject.QCProjects.QCPeriods = await CreateQCPeriodsAsync(baseProject);
            var qcPeriods = await _baseProjectRepository.GetQCPeriodAsync(baseProjectId);
            var result = await _baseProjectRepository.UpdateAsync(baseProjectId, editBaseProject, isPGDeleted);
            if (isPGDeleted)
            {
               
                foreach (var qcPeriod in qcPeriods)
                {
                    var SyncId = $"{qcPeriod.QCProjectId}-{qcPeriod.PeriodId}";
                    await _outBoxItemRepository.SaveMessagesAsync(
                    new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = SyncId },
                    ProjectMessageType.QCPeriodDelete);
                }

                foreach (var qcPeriod in result.QCProjects.QCPeriods)
                {
                    await _outBoxItemRepository.SaveMessagesAsync(
                    new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = qcPeriod.Id },
                    ProjectMessageType.QCPeriodCreate);
                }
            }
            if (result != null)
            {
                await PublishOutboxMessagesAsync(result.Id);
            }

            return result;
        }


        private async Task CheckDeletedProductGroupIssuesAsync(List<int> deletedProductGroups, int baseProjectId)
        {
            if (deletedProductGroups.Count == 0)
                return;

            var dataloadedpgs = await _baseProjectRepository.CheckDataLoading(deletedProductGroups, baseProjectId);
            var pgswithRBBP = await _baseProjectRepository.CheckProductGroupRBBPDependency(deletedProductGroups, baseProjectId);

            if (dataloadedpgs.Length == 0 && pgswithRBBP.Count == 0)
                return;

            var issues = new List<ProductGroupIssue>();

            foreach (var pgId in dataloadedpgs)
            {
                issues.Add(new ProductGroupIssue
                {
                    ProductGroupId = pgId,
                    ProductGroupDesc = null,
                    HasDataLoaded = true,
                    RbProjectIds = new List<int>()
                });
            }

            var rbbpGrouped = pgswithRBBP
                .GroupBy(p => new { p.ProductGroupId, p.ProductGroupDesc })
                .ToDictionary(
                    g => g.Key.ProductGroupId,
                    g => new ProductGroupIssue
                    {
                        ProductGroupId = g.Key.ProductGroupId,
                        ProductGroupDesc = g.Key.ProductGroupDesc,
                        HasDataLoaded = dataloadedpgs.Contains(g.Key.ProductGroupId),
                        RbProjectIds = g.Select(x => x.RbProjectId).Distinct().ToList()
                    });

            foreach (var issue in rbbpGrouped.Values)
            {
                var existing = issues.FirstOrDefault(i => i.ProductGroupId == issue.ProductGroupId);
                if (existing == null)
                {
                    issues.Add(issue);
                }
                else
                {
                    existing.RbProjectIds = issue.RbProjectIds;
                    existing.ProductGroupDesc ??= issue.ProductGroupDesc;
                }
            }

            if (issues.Count > 0)
            {
                throw new InvalidOperationException(JsonSerializer.Serialize(issues));
            }
        }
        private async Task PublishOutboxMessagesAsync(int baseProjectId)
        {
            var data = new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = baseProjectId };

            await _outBoxItemRepository.SaveMessagesAsync(data, ProjectMessageType.BaseProjectUpdate);
            await _outBoxItemRepository.SaveMessagesAsync(data, ProjectMessageType.QCProjectUpdate);
        }

        public async Task<BaseProject> GetAsync(int baseProjectId)
        {
            var result = await _baseProjectRepository.GetAsync(baseProjectId);
            if (result==null)
                throw new EntityNotExistsException ($"Base project with ID {baseProjectId} not found." );

            var retailerSeparationState = await _baseProjectRepository.CheckRetailerSeparationState(baseProjectId);

            result.IsRetailerSeparationRequested = retailerSeparationState;
            return result;
        }

        public async Task<BaseProjectLists> GetAsyncList(BaseProjectsLists baseProjectsLists)
        {
            var baseProjectListDto = new BaseProjectLists();
            var baseProjectList = await _baseProjectRepository.GetAsyncList(baseProjectsLists);

            baseProjectListDto.Counts = baseProjectList.Count();
            baseProjectListDto.MoreRecordsAvailable = false;

            ICollection<BaseProjectListPayloadResponse> baseProjectRecordDto =
                _mapper.Map<ICollection<BaseProjectListPayloadResponse>>(baseProjectList);


            baseProjectListDto.Records = baseProjectRecordDto;
            return baseProjectListDto;
        }

        public async Task<IReadOnlyList<Dependencies>> DeleteAsync(BaseProjectDeletes baseProjectDeleteDto)
        {
            var responses = new List<Dependencies>();

            foreach (var bpId in baseProjectDeleteDto.Ids)
            {
                var dependencyMessage = await _baseProjectRepository.CheckDependencies(bpId);
                //QC Status Check work
                var qcPeriodIdwithQCStatus = await CheckQCStatus(bpId);
                if (dependencyMessage.ProductionProjectIds.Count() != 0 || dependencyMessage.RBBaseProjectIds.Count() != 0 || dependencyMessage.ReportingProjectIds.Count() != 0 || qcPeriodIdwithQCStatus!=0)
                {
                    dependencyMessage.QCStatusPeriodId = qcPeriodIdwithQCStatus;
                    responses.Add(new Dependencies(bpId.ToString(), string.Empty, (int)HttpStatusCode.BadRequest, "", dependencyMessage));
                }

                var retailerSeparationState = await _baseProjectRepository.CheckRetailerSeparationState(bpId);
                if (retailerSeparationState)
                {
                    ProjectsDependencies dependencies = new ProjectsDependencies();
                    dependencies = null;
                    responses.Add(new Dependencies(bpId.ToString(), string.Empty, (int)HttpStatusCode.BadRequest, "BaseProject Can't be deleted because it is part of a pending Retailer Separation Request", dependencies));
                }
            }
            var idsToDelete = baseProjectDeleteDto.Ids.Except(responses.Select(r => Convert.ToInt32(r.BaseProjectId))).ToList();
            if (idsToDelete.Any())
            {
                var deleteResponses = await _baseProjectRepository.DeleteAsync(idsToDelete, baseProjectDeleteDto.DeletedBy);
                responses.AddRange(deleteResponses);
            }

            if (responses.Any(response => response.StatusCode == StatusCodes.Status200OK))
            {
                var successfulBPIds = responses.Where(response => response.StatusCode == StatusCodes.Status200OK)
                                              .Select(response => Convert.ToInt32(response.BaseProjectId))
                                              .ToList();

                var successfulQCIds = responses.Where(response => response.StatusCode == StatusCodes.Status200OK)
                                              .Select(response => Convert.ToInt32(response.QCProjectId))
                                              .ToList();

               
                if (successfulBPIds.Any())
                {
                    foreach (var baseProjectId in successfulBPIds)
                    {
                        await _outBoxItemRepository.SaveMessagesAsync(
                            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = baseProjectId },
                            ProjectMessageType.BaseProjectDelete);
                    }
                }

                if (successfulQCIds.Any())
                {
                    foreach (var qcProjectId in successfulQCIds)
                    {
                        await _outBoxItemRepository.SaveMessagesAsync(
                            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = qcProjectId },
                            ProjectMessageType.QCProjectDelete);
                    }
                }
            }
            return responses;
        }

        private async Task<long> CheckQCStatus(int BaseProjectId)
        {
            var responses = new List<Dependencies>();

            QCStatuses qcStatuses = new QCStatuses
            {
                BaseProjectIds = new List<int> { BaseProjectId }
            };
            var qcStatusBps = await _baseProjectRepository.GetQCPeriodWithQCStatus(qcStatuses);

            if (qcStatusBps.Count!=0)
            {
                return qcStatusBps[0];
            }

            return 0;
        }

        private async Task<List<QCPeriod>> CreateQCPeriodsInternalAsync(BaseProject baseProject)
        {
            var qcPeriods = await _operationHelper.GetPeriodsAsync(baseProject.PeriodicityId);

            var qcPeriodsList = new List<Period>
            {
                new Period
                {
                    RefProjectId= baseProject.Id,
                    RefPeriodId = qcPeriods[1].Id,
                    index = 0
                }
            };

            if (baseProject.PeriodicityId != (int)PeriodicityType.Daily)
            {
                if (!Dictionary.PeriodicityMapping.TryGetValue(baseProject.PeriodicityId, out int periodicityIndex))
                {
                    throw new ArgumentOutOfRangeException(nameof(baseProject.PeriodicityId), $"Not expected PeriodicityId value: {baseProject.PeriodicityId}");
                }

                qcPeriodsList.Add(new Period
                {
                    RefProjectId = baseProject.Id,
                    RefPeriodId = qcPeriods[periodicityIndex].Id,
                    index = 6
                });
            }

            var qcPeriod = new QCPeriod
            {
                PeriodId = qcPeriods[0].Id,
                Periods = qcPeriodsList,
                CreatedBy = baseProject.CreatedBy,
                UpdatedBy = baseProject.UpdatedBy,
                CreatedWhen = DateTimeOffset.UtcNow,
                StockInitialization = new StockInitialization
                {
                    StockBaseProjectId = null,
                    StockPeriodId = null
                }
            };

            return new List<QCPeriod> { qcPeriod };
        }

        private async Task<List<QCPeriod>> CreateQCPeriodsInternalWithCacheAsync(BaseProject baseProject, Dictionary<int, dynamic> periodicityCache)
        {
            dynamic qcPeriods;
            if (periodicityCache.TryGetValue(baseProject.PeriodicityId, out qcPeriods))
            {
                _logger.LogDebug($"Using cached periods for periodicity {baseProject.PeriodicityId}");
            }
            else
            {
                _logger.LogDebug($"Fetching periods from API for periodicity {baseProject.PeriodicityId}");
                qcPeriods = await _operationHelper.GetPeriodsAsync(baseProject.PeriodicityId);
            }

            var qcPeriodsList = new List<Period>
            {
                new Period
                {
                    RefProjectId= baseProject.Id,
                    RefPeriodId = qcPeriods[1].Id,
                    index = 0
                }
            };

            if (baseProject.PeriodicityId != (int)PeriodicityType.Daily)
            {
                if (!Dictionary.PeriodicityMapping.TryGetValue(baseProject.PeriodicityId, out int periodicityIndex))
                {
                    throw new ArgumentOutOfRangeException(nameof(baseProject.PeriodicityId), $"Not expected PeriodicityId value: {baseProject.PeriodicityId}");
                }

                qcPeriodsList.Add(new Period
                {
                    RefProjectId = baseProject.Id,
                    RefPeriodId = qcPeriods[periodicityIndex].Id,
                    index = 6
                });
            }

            var qcPeriod = new QCPeriod
            {
                PeriodId = qcPeriods[0].Id,
                Periods = qcPeriodsList,
                CreatedBy = baseProject.CreatedBy,
                UpdatedBy = baseProject.UpdatedBy,
                CreatedWhen = DateTimeOffset.UtcNow,
                StockInitialization = new StockInitialization
                {
                    StockBaseProjectId = null,
                    StockPeriodId = null
                }
            };

            return new List<QCPeriod> { qcPeriod };
        }

        private async Task<QCProject> CreateQCProjectModelAsync(BaseProject baseProject)
        {
            var qcPeriods = await CreateQCPeriodsInternalAsync(baseProject);

            return new QCProject
            {
                IsAutoLoad = baseProject.QCProjects.IsAutoLoad,
                IsAutomatedPriceCheck = baseProject.QCProjects.IsAutomatedPriceCheck,
                SQCMode = baseProject.QCProjects.SQCMode,
                ResetCorrectionTypeId = baseProject.QCProjects.ResetCorrectionTypeId,
                QCPeriods = qcPeriods
            };
        }

        private async Task<QCProject> CreateQCProjectModelWithLoadedDataAsync(BaseProject baseProject, Dictionary<int, dynamic> periodicityCache)
        {
            var qcPeriods = await CreateQCPeriodsInternalWithCacheAsync(baseProject, periodicityCache);

            return new QCProject
            {
                IsAutoLoad = baseProject.QCProjects.IsAutoLoad,
                IsAutomatedPriceCheck = baseProject.QCProjects.IsAutomatedPriceCheck,
                SQCMode = baseProject.QCProjects.SQCMode,
                ResetCorrectionTypeId = baseProject.QCProjects.ResetCorrectionTypeId,
                QCPeriods = qcPeriods
            };
        }

        private Task<List<QCPeriod>> CreateQCPeriodsAsync(BaseProject baseProject)
        {
            return CreateQCPeriodsInternalAsync(baseProject);
        }




        private async Task SendBPToOutBox(BaseProject result, int? panelId = null)
        {
          


            await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.Id },

            ProjectMessageType.BaseProjectCreate);

            if (panelId != (int)PanelType.IDAS && result.QCProjects != null)
            {


                        await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.QCProjects.BaseProjectId },
            ProjectMessageType.QCProjectCreate);

                if (result.QCProjects?.QCPeriods != null && result.QCProjects.QCPeriods.Any() && result.QCProjects.QCPeriods.First() != null)
                {

                await  _outBoxItemRepository.SaveMessagesAsync(
                        new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.QCProjects.QCPeriods.First().Id },
                        ProjectMessageType.QCPeriodCreate);     
                        
                }
            }
        }
        public async Task<ProjectLists> GetAsyncListBaseProjects(BaseProjectNameandIdLists baseProjectsLists)
        {
            var baseProjectListDto = new ProjectLists();
            var baseProjectList = await _baseProjectRepository.GetAsyncListBaseProjects(baseProjectsLists);

            baseProjectListDto.Counts = baseProjectList.Count();
            baseProjectListDto.MoreRecordsAvailable = false;

            ICollection<BaseProjectNameandIdResponse> baseProjectRecordDto =
                _mapper.Map<ICollection<BaseProjectNameandIdResponse>>(baseProjectList);

            baseProjectListDto.Records = baseProjectRecordDto;
            return baseProjectListDto;
        }

        public async Task<List<int>> GetAsync(BaseProjectCountries baseProjectCountryRequest)
        {
            var result = await _baseProjectRepository.GetByBaseProjectIdAsync(baseProjectCountryRequest);
            return result;
        }

        public async Task<IReadOnlyList<string>> GetUsersList()
        {
            var usersList = await _baseProjectRepository.GetUsersList();
            return usersList;
        }

        public async Task<IReadOnlyList<Dependencies>> AddBulkAsync(List<int> baseProjectIds, string userName, string countryIds = null)
        {
            var commonPeriodicities = new[]
            {
                PeriodicityType.Weekly,
                PeriodicityType.Monthly,
                PeriodicityType.TwoMonthlyStartJan,
                PeriodicityType.FourMonthlyStartJan,
                PeriodicityType.ThreeMonthlyStartJan
            };

            var PreLoadedperiodicity = new Dictionary<int, dynamic>();

            foreach (var periodicity in commonPeriodicities)
            {
                try
                {
                    var periods = await _operationHelper.GetPeriodsAsync((int)periodicity);
                    PreLoadedperiodicity[(int)periodicity] = periods;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to cache periodicity {periodicity}");
                }
            }

            var sourceBaseProjects = await _baseProjectRepository.GetSourceBaseProjectsWithRelatedDataAsync(baseProjectIds);
            var qcProjectsMap = new Dictionary<int, QCProject>();

            foreach (var sourceBaseProject in sourceBaseProjects)
            {
                if (sourceBaseProject.PanelId != (int)PanelType.IDAS && sourceBaseProject.QCProjects != null)
                {
                    var tempBaseProject = new BaseProject
                    {
                        Id = 0, 
                        PeriodicityId = sourceBaseProject.PeriodicityId,
                        CreatedBy = userName,
                        UpdatedBy = userName,
                        QCProjects = new QCProject
                        {
                            ResetCorrectionTypeId = sourceBaseProject.QCProjects.ResetCorrectionTypeId,
                            IsAutoLoad = sourceBaseProject.QCProjects.IsAutoLoad,
                            SQCMode = sourceBaseProject.QCProjects.SQCMode,
                            IsAutomatedPriceCheck = sourceBaseProject.QCProjects.IsAutomatedPriceCheck
                        }
                    };

                    var qcProject = await CreateQCProjectModelWithLoadedDataAsync(tempBaseProject, PreLoadedperiodicity);
                    qcProjectsMap[sourceBaseProject.Id] = qcProject;
                }
            }

            var result = await _baseProjectRepository.AddBulkAsync(baseProjectIds, userName, qcProjectsMap, sourceBaseProjects);
            var successfulCreations = result.Where(r => r.StatusCode == StatusCodes.Status201Created).ToList();

            if (successfulCreations.Any())
            {
                try
                {
                    var createdBaseProjectIds = successfulCreations
                        .Where(s => int.TryParse(s.QCProjectId, out _))
                        .Select(s => int.Parse(s.QCProjectId))
                        .ToArray();

                    if (createdBaseProjectIds.Any())
                    {
                        var baseProjectsListRequest = new BaseProjectsLists
                        {
                            BaseProjectIDs = createdBaseProjectIds,
                            Deleted = false,
                            Limit = createdBaseProjectIds.Length
                        };
                        var createdBaseProjects = await _baseProjectRepository.GetAsyncList(baseProjectsListRequest);

                        await CallProjectSecurityBulkCopyAsync(baseProjectIds, createdBaseProjectIds, userName, countryIds);

                        foreach (var createdBaseProject in createdBaseProjects)
                        {
                            await SendBPToOutBox(createdBaseProject, createdBaseProject.PanelId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending outbox messages for created BaseProjects");
                }
            }

            return result;
        }

        private async Task CallProjectSecurityBulkCopyAsync(List<int> sourceBaseProjectIds, int[] createdBaseProjectIds, string userName, string countryIds)
        {
            try
            {
                var projectMappings = new List<ProjectMapping>();

                for (int i = 0; i < sourceBaseProjectIds.Count && i < createdBaseProjectIds.Length; i++)
                {
                    projectMappings.Add(new ProjectMapping
                    {
                        SourceProjectId = sourceBaseProjectIds[i],
                        NewBaseProjectId = createdBaseProjectIds[i]
                    });
                }

                var bulkCopyRequest = new BulkCopyProjectUserRequest
                {
                    ProjectTypeId = ProjectServicesSecurityConstants.BPSecurity, 
                    ProjectMappings = projectMappings
                };

                var response = await _securityHelper.BulkCopyProjectUsersAsync(bulkCopyRequest, userName, countryIds);

                _logger.LogInformation($"Successfully called ProjectSecurity BulkCopy for {projectMappings.Count} BaseProjects");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling ProjectSecurity BulkCopy API");
            }
        }

    }
}
