﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces
{
    public interface IOutBoxItemRepository
    {
        Task SaveMessagesAsync(object message, ProjectMessageType messageTypeId);
        Task SaveBulkMessagesAsync(List<(object message, ProjectMessageType messageTypeId)> messages);
        Task<List<OutBoxItemEntity>> GetUnprocessedMessagesAsync();
        Task MarkMessageAsProcessedAsync(Guid messageId);
    }
}
