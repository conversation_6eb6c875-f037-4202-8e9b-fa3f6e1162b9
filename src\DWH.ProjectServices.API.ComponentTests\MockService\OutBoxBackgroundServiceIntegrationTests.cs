﻿using AutoFixture;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Models;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class OutBoxBackgroundServiceIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly Mock<IRabbitMQSender> _mockRabbitMqSender;
        private readonly Fixture _fixture;

        public OutBoxBackgroundServiceIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _mockRabbitMqSender = new Mock<IRabbitMQSender>();
            _fixture = new Fixture();
        }

        [Fact]
        public async Task ExecuteAsync_ShouldProcessOutboxMessages()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var testMessageId = Guid.NewGuid();
            var testMessage = new OutBoxItemEntity
            {
                Id = testMessageId,
                Payload = JsonSerializer.Serialize(new { Id = testMessageId, SyncingEntityId = "111-123" }),
                TypeId = ProjectMessageType.BaseProjectCreate.ToString(),
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            dbContext.OutBoxItem.Add(testMessage);
            await dbContext.SaveChangesAsync();

            var backgroundService = new OutBoxBackgroundService(
                scope.ServiceProvider.GetRequiredService<IServiceScopeFactory>(),
                Mock.Of<ILogger<OutBoxBackgroundService>>(),
                _mockRabbitMqSender.Object
            );

            using var cts = new CancellationTokenSource();

            // Act
            await backgroundService.StartAsync(cts.Token);
            await Task.Delay(20000); // Wait for processing

            var processedMessage = await dbContext.OutBoxItem.AsNoTracking()
                .FirstOrDefaultAsync(m => m.Id == testMessageId);

            // Assert
            processedMessage.Should().NotBeNull();
            processedMessage.Status.Should().Be(OutboxStatus.Processed);

            _mockRabbitMqSender.Verify(sender =>
                sender.SendToRabbitMQ(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<ProjectServicesData>()),
                Times.AtLeastOnce);
        }
    }
}
