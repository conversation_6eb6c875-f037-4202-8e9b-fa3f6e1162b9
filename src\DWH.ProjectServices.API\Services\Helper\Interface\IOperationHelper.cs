﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Helper.Records;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Domain.Enum;

namespace DWH.ProjectServices.API.Services.Helper.Interface
{
    public interface IOperationHelper
    {
        Task<List<PeriodDistance>> CalculateDistancesAsync(IEnumerable<Period> periods, long periodId);
        Task<QCPeriod> GetRecentQCPeriodsAsync(int qcProjectId);
        Task<List<ShiftedPeriod>> CalculateShiftedPeriodsAsync(long targetPeriodId, List<PeriodDistance> distances);
        Task<QCPeriod> GetBaseQCPeriodAsync(int qcProjectId, long periodId);
        Task<string> GetPeriodShortNameAsync(long PeriodId);
        Task<List<long>> GetPeriodRangeAsync(int PeriodicityId, string StartPeriod, string EndPeriod);
        Task<int> GetPeriodicityIdAsync(int baseProjectId);
        Task<string> ExtractAndFormatEndDateAsync(string name);
        Task<string> GetPeriodNameAsync(long PeriodId);
        Task<string> ParseAndFormatDateAsync(string dateString);
        Task<string> ExtractAndFormatEndDateAsyncMonthy(string name);
        Task<string> ParseAndFormatDateAsyncMonthly(string dateString);
        Task<List<ShiftedPeriodResponse>> GetPeriodsAsync(int PeriodicityId);
        Task<List<ShiftedPeriodResponse>> GetPeriodsAsyncForBulkQC(int PeriodicityId);

        Task<BaseChannelRearrangements> GetBCRDetails(string endpoint, int[] productGroups);
        Task<List<ShiftedPeriod>> GetShiftedPeriodsForProjectAsync(List<Period> baseRefPeriods, int offset);

    }
}
