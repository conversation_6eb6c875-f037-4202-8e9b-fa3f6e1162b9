﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Newtonsoft.Json;
using System.Drawing;
using System.Net;
using System.Text;

namespace DWH.ProjectServices.API.Services.Helper
{
    public class SecurityHelper : ISecurityHelper
    {
            private const string API_ENDPOINT_POST = "/api/projectuser";
            private const string API_ENDPOINT_GET = "/api/projectuser/{0}";
            private readonly IQCSecurityAPIClient _securityApiClient;

            public SecurityHelper(IQCSecurityAPIClient securityapiClient)
            {
                _securityApiClient = securityapiClient;
            }

        public async Task<AssignUsersToProjectResponse> AssignUsersToProject(AssignUsersToProjectDetails assignUsersToProjectDetails,string userName)
        {
            var requestContent = new StringContent(JsonConvert.SerializeObject(assignUsersToProjectDetails), Encoding.UTF8, "application/json");
            var response = await _securityApiClient.PostAsync<AssignUsersToProjectResponse>(API_ENDPOINT_POST, requestContent,userName);  
            
            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }
            return response.Data;
          
        }



        public async Task<ProjectUserDetails> GetUsersByProjectId(int qcProjectId)
            {
                var endpoint = string.Format(API_ENDPOINT_GET, qcProjectId);

                var response = await _securityApiClient.GetAsync<ProjectUserDetails>(endpoint);

                 if (!response.IsSuccess)
            {
                if (response.StatusCode == HttpStatusCode.NotFound)
                {

                    return null;
                }
                throw new HttpRequestException($"Error calling API to get users by project ID: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
                }

                return response.Data;
            }
        public async Task<bool> AssignUsersToNewProject(int sourceProjectId, int targetProjectId,string userName,int projectTypeId)
        {
            var sourceProjectUsers = await GetUsersByProjectId(sourceProjectId);
            if (sourceProjectUsers== null)
            {
                return false; 
            }

            var assignUsersToProjectDetails = new AssignUsersToProjectDetails
            {
                ProjectTypeId = projectTypeId,
                ProjectIds = new List<int> { targetProjectId },
                UserIds = sourceProjectUsers.Users.Select(user => user.Id).ToList()
            };
            var assignResponse = await AssignUsersToProject(assignUsersToProjectDetails,userName);
            return assignResponse != null;
        }

    }

}

