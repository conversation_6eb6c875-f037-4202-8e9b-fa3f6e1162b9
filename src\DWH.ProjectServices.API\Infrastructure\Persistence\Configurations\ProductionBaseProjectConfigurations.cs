﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class ProductionBaseProjectConfigurations : IEntityTypeConfiguration<ProductionBaseProject>
    {
        public void Configure(EntityTypeBuilder<ProductionBaseProject> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_BASEPRODPROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("PRODPROJECTID");
            builder.Property(p => p.BaseProjectId).HasColumnName("BASEPROJECTID");
            builder.Property(p => p.Deleted).HasColumnName("DELETED");

        }
    }
}
