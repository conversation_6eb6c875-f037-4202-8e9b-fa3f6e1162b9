﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;
using GfK.Dwh.LoadMonitor.Api.StartupExtensions;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using Oracle.ManagedDataAccess.Client;
using System.Data;
using System.Linq;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class ProductGroupRepository : IProductGroupRepository
    {
        private readonly OracleDbContext _dbContext;

        public ProductGroupRepository(OracleDbContext dbContext) => _dbContext = dbContext;

        public async Task<IEnumerable<ProductGroup>> GetAllAsync(int countryId) =>
                         await _dbContext.ProductGroups.Where(x => x.CountryId == countryId).ToListAsync();


        public async Task<IEnumerable<ProductGroup>> GetAllAsync(ProductGroupRequest pgRequestDto)
        {
            var query = _dbContext.ProductGroups
                                    .WhereIfExists(i => pgRequestDto.CountryIds.Contains(i.CountryId), pgRequestDto.CountryIds)
                                    .WhereIfExists(i => pgRequestDto.SectorIds.Contains(i.SectorId), pgRequestDto.SectorIds)
                                    .WhereIfExists(i => pgRequestDto.CategoryIds.Contains(i.CategoryId), pgRequestDto.CategoryIds)
                                    .WhereIfExists(i => pgRequestDto.DomainProductGroupIds.Contains(i.DomainProductGroupId), pgRequestDto.DomainProductGroupIds)
                                    .Where(i => i.Deleted == 0);

            if (pgRequestDto.PanelIds.Length > 1)
            {
                query = query.WhereIfExists(i => pgRequestDto.PanelIds.Contains(i.PanelId), pgRequestDto.PanelIds);
            }
            else
            {
                if (pgRequestDto.PanelIds[0] == (int)PanelType.TS)
                {
                    query = query.Where(i => i.PanelId == (int)PanelType.TS && i.HybridFlag == 0);
                }
                else
                {
                    query = query.Where(i => i.PanelId == (int)PanelType.POS || i.HybridFlag == -1);
                }
            }

            var result = await query.OrderBy(i => i.Description).ToListAsync();
            if (!result.Any())
                throw new EntityNotExistsException($"ProductGroup Not Found ");
            return result;
        }

        public string GetPeriodicityDesc(int periodicityId)
        {

            const string selectPeriodicityQuery = "select p.PERIODICITY_ID, p.PERIODICITY_DDESC from DWH_META.ADM_PE_PERIODICITY p where p.PERIODICITY_ID = :periodicityId";

            var parameters = new[]
            {
                    new OracleParameter("periodicityId", periodicityId)
                };

            var periodicityData = _dbContext.PeriodicityDesc
                                            .FromSqlRaw(selectPeriodicityQuery, parameters)
                                            .SingleOrDefault();
            if (periodicityData != null)
            {
                return periodicityData.PERIODICITY_DDESC;
            }

            else
                throw new EntityNotExistsException($"Periodicity data Not Found ");

        }

        public async Task<IEnumerable<DomainProductGroup>> GetDomainProductGroupAsync(DescriptionRequest descriptionDto)
        {
            string selectDominProductGroupQuery = @"SELECT dpg.DOMAIN_PRODUCTGROUP_ID, dpg.DOMAIN_PRODUCTGROUP_DDESC, aps.SECTOR_SDESC
                                                    FROM DWH_META.ADM_PG_DOMAIN_PRODUCTGROUP dpg
                                                    INNER JOIN DWH_META.ADM_PG_PRODUCTGROUP pg
                                                    ON dpg.DOMAIN_PRODUCTGROUP_ID = pg.DOMAIN_PRODUCTGROUP_ID
                                                    INNER JOIN DWH_META.ADM_PG_Sector aps
                                                    ON dpg.SECTOR_ID = aps.SECTOR_ID
                                                    WHERE pg.PRODUCTGROUP_ID IN (SELECT TO_NUMBER(REGEXP_SUBSTR(:productIds, '[^,]+', 1, LEVEL)) 
                                                                                    FROM DUAL CONNECT BY REGEXP_SUBSTR(:productIds, '[^,]+', 1, LEVEL) 
                                                                                    IS NOT NULL)";
            var parameters = new[]
            {
                    new OracleParameter("productIds", OracleDbType.Varchar2)
                    {
                        Value = string.Join(",", descriptionDto.ProductGroupIds)
                    }
                };

            var dpgData = await _dbContext.DomainProductGroups
                                            .FromSqlRaw(selectDominProductGroupQuery, parameters)
                                            .ToListAsync();

            if (!dpgData.Any())
                throw new EntityNotExistsException($"ProductGroup Not Found ");

            return dpgData;
        }
    }
}
