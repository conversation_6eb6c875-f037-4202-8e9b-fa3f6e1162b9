﻿using AutoFixture;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver.Core.Configuration;
using System.Net;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using WireMock.Server;
using DWH.ProjectServices.API.Services.Helper;
using Microsoft.Extensions.Options;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Google.Protobuf.WellKnownTypes;
using System.Net.Http.Headers;
using WireMock.Settings;
using Microsoft.AspNetCore.Mvc;
using DWH.ProjectServices.API.Domain.Enum;
using Microsoft.VisualStudio.TestPlatform.Utilities;
using Xunit.Abstractions;
using DWH.ProjectServices.API.Domain.Models;
using Npgsql;
using DWH.ProjectServices.API.Services;


namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class BaseProjectControllerCTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private const string PATH = "/api/v1/BaseProjects";

        private const string GERMANY_COUNTRY_ID = "15";
        private const string EXPECTED_MEDIA_TYPE = "application/json";

        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;
        private readonly WireMockManager _wireMockManager;
        private readonly Fixture _fixture;
        private readonly ITestOutputHelper _output;
       
        public BaseProjectControllerCTest(WebApplicationFactory<Program> factory, ITestOutputHelper output)
        {
            _fixture = new Fixture();
            _factory = factory;
            _wireMockManager = new WireMockManager();
            _output = output;

            _client = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    services.AddHttpClient<IAdministratorAPIClient, AdministratorAPIClient>((serviceProvider, client) =>
                    {
                        var wireMockUrl = _wireMockManager.Server.Urls[0];
                        client.BaseAddress = new Uri(wireMockUrl);

                        // Override WebServiceClientOptions to ensure base address points to WireMock server
                        var options = serviceProvider.GetRequiredService<IOptions<WebServiceClientOptions>>().Value;
                        options.BaseAddress.DateAPI = wireMockUrl;
                    });
                });
            }).CreateClient();
        }

        [Fact]
        public async Task AddAsync_BaseProject_Returns_Success_Response()
        {
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 1).With(x => x.ResetCorrectionTypeId, 3));

            var body = _fixture.Create<BaseProjectCreateRequest>();

            var response = await _client.PostAsJsonAsync(PATH, body);

            response.Should().NotBeNull();
            response.StatusCode.Should().Be(HttpStatusCode.Created);

            var createdProject = await response.Content.ReadFromJsonAsync<BaseProjectResponse>(); // Assuming BaseProjectResponse with QCPeriods info
            createdProject.Should().NotBeNull();
            createdProject.QCProjects.QCPeriods.Should().NotBeNull();
        }

        [Fact]
        public async Task AddAsync_BaseProject_ArgumentOutOfRangeException()
        {
            // Arrange
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            _fixture.Customize<BaseProjectCreateRequest>(b => b
                .With(x => x.PanelId, 1)
                .With(x => x.CountryId, 15)
                .With(x => x.PeriodicityId, 999)
                .With(x => x.ResetCorrectionTypeId, 3));

            var body = _fixture.Create<BaseProjectCreateRequest>();

            // Act
            var response = await _client.PostAsJsonAsync(PATH, body);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        }

        [Fact]
        public async Task AddAsync_BaseProject_ExternalApiFailure()
        {
            // Arrange
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            _fixture.Customize<BaseProjectCreateRequest>(b => b
                .With(x => x.PanelId, 1)
                .With(x => x.CountryId, 15)
                .With(x => x.PeriodicityId, 1)
                .With(x => x.ResetCorrectionTypeId, 3));

            _wireMockManager.Dispose(); //stop server to test api failure case

            var body = _fixture.Create<BaseProjectCreateRequest>();

            // Act
            var response = await _client.PostAsJsonAsync(PATH, body);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);
        }


        [Fact]
        public async Task GetAsync_BaseProject_ById_Returns_BaseProject_Success_Response()
        {
            var client = _factory.CreateClient();

            //Create a Base project with country id that ll be used later

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 4).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 13).With(x => x.DataTypeId, 1).With(x => x.PurposeId, 1));

            var body = _fixture.Create<BaseProjectCreateRequest>();

            var createdresponse = await client.PostAsJsonAsync(PATH, body);
            var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);
            
            // Adding headers to the request
            client.DefaultRequestHeaders.Add("Custom-Countryid", responseObject.CountryId.ToString());
            
            
            //Get Base project with that country
            var response = await client.GetAsync(Path.Combine("/api/v1/BaseProjects/BaseProject", responseObject.Id.ToString()));

            var responseContent = await response.Content.ReadAsStringAsync();

            var result = JsonSerializer.Deserialize<BaseProjectResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();

        }

        [Fact]
        public async Task GetAsync_BaseProject_ById_Returns_BaseProject_Unauthorized_Response()
        {
            var client = _factory.CreateClient();

            //Create a Base project with country id that ll be used later

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 4).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 13).With(x=>x.DataTypeId,1).With(x => x.PurposeId, 1));

            var body = _fixture.Create<BaseProjectCreateRequest>();

            var createdresponse = await client.PostAsJsonAsync(PATH, body);
            var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);

            // Adding headers to the request
            client.DefaultRequestHeaders.Add("Custom-Countryid", "1"); //Giving an id of country that doesnt exist for this test case


            var response = await client.GetAsync(Path.Combine("/api/v1/BaseProjects/BaseProject", responseObject.Id.ToString()));

            var responseContent = await response.Content.ReadAsStringAsync();
            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

        }

        [Fact]
        public async Task GetAllAsync_BaseProject_Returns_Success_Response()
        {
            var client = _factory.CreateClient();
            var body = new BaseProjectListRequest();
            body.Id = 1;

            var response = await client.PostAsJsonAsync("/api/v1/BaseProjects/List", body);

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var result = JsonSerializer.Deserialize<BaseProjectListResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();

        }

        [Fact]
        public async Task GetAllAsync_BaseProject_Returns_Filtered_Response()
        {
            var client = _factory.CreateClient();
            var body = new BaseProjectListRequest();
            body.CountryIds = new int[] { 935 };
            var unfilteredresponse = await client.PostAsJsonAsync("/api/v1/BaseProjects/List", body);

            var unfilteredresponseContent = await unfilteredresponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var unfilteredresult = JsonSerializer.Deserialize<BaseProjectListResponse>(unfilteredresponseContent, options);


            //With CountryId header with different id
            body = new BaseProjectListRequest();
            body.CountryIds = new int[] { 935 };
            client.DefaultRequestHeaders.Add("Custom-Countryid", "15");
            var response = await client.PostAsJsonAsync("/api/v1/BaseProjects/List", body);

            var responseContent = await response.Content.ReadAsStringAsync();
            options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var filteredresult = JsonSerializer.Deserialize<BaseProjectListResponse>(responseContent, options);

            unfilteredresponse.StatusCode.Should().Be(HttpStatusCode.OK);
            filteredresult.Records.Should().BeNull();
            unfilteredresult.Records.Should().NotBeNull();

        }

        [Fact]
        public async Task GetAllAsync_BaseProject_WhenNotExists_Response()
        {
            var client = _factory.CreateClient();
            var body = _fixture.Create<BaseProjectListRequest>();

            var response = await client.PostAsJsonAsync("/api/v1/BaseProjects/List", body);

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var result = JsonSerializer.Deserialize<BaseProjectListResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
            result?.Records.Should().BeNull();
            result?.Counts.Equals(0);
        }

        [Fact]
        public async Task UpdateAsync_BaseProject_Returns_BaseProject_With_Success_Response()
        {
            var client = _factory.CreateClient();

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 4).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 13).With(x => x.DataTypeId, 1).With(x => x.PurposeId, 1));

            var body = _fixture.Create<BaseProjectCreateRequest>();


            var createResponse = await client.PostAsJsonAsync(PATH, body);
            createResponse.EnsureSuccessStatusCode();
            var createdProjectContent = await createResponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var createdProject = JsonSerializer.Deserialize<BaseProjectResponse>(createdProjectContent, options);

            var editRequest = new BaseProjectEditRequest
            {
                Name = "TextUpdated",
                Predecessors = _fixture.CreateMany<BaseProjectPredecessorModelDto>(6).ToList(),
            };

            // Add headers and send the update request
            var response = await client.PutAsJsonAsync($"/api/v1/BaseProjects/{createdProject.Id}", editRequest);

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseProjectEditResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);

            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
        }


        [Fact]
        public async Task DeleteAsync_BaseProject_Returns_Success_Response()
        {
            var client = _factory.CreateClient();

            //Create a Base project with country id that ll be used later

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 15));

            var body = _fixture.Create<BaseProjectCreateRequest>();

            var createdresponse = await client.PostAsJsonAsync(PATH, body);
            var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);


            List<int> baseProjectIds = new List<int> { responseObject.Id };
            _fixture.Customize<BaseProjectDeleteRequest>(b => b.With(x => x.Ids, baseProjectIds));
            var deletebody = _fixture.Create<BaseProjectDeleteRequest>();

            var stringContent = new StringContent(JsonSerializer.Serialize(deletebody), Encoding.UTF8, "application/json");
            var request = new HttpRequestMessage(HttpMethod.Delete, PATH);

            request.Content = stringContent;

            var response = await client.SendAsync(request);
            
            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
        }

        [Fact]
        public async Task DeleteAsync_BaseProject_Returns_BadRequest_Response()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<OracleDbContext>();
                var client = _factory.CreateClient();

                client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 15));

                var body = _fixture.Create<BaseProjectCreateRequest>();

                var createdresponse = await client.PostAsJsonAsync(PATH, body);
                var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);

                //add reporting project data to test dependency with this baseprojectid
                var reportingProjects = new ReportingBaseProject
                {
                    BaseProjectId = responseObject.Id,
                    Deleted = 0,
                    Id = _fixture.Create<int>(),
                    CreatedBy="IntegrationTest",
                    CreatedWhen= "10-MAR-22",
                    ChangedBy = "IntegrationTest",
                    ChangedWhen = "10-MAR-22"
                };
                var mydata= dbContext.ReportingBaseProjects.Add(reportingProjects);
                await dbContext.SaveChangesAsync();
                
                //SetupDelete
                List<int> baseProjectIds = new List<int> { responseObject.Id };
                _fixture.Customize<BaseProjectDeleteRequest>(b => b.With(x => x.Ids, baseProjectIds));
                var deletebody = _fixture.Create<BaseProjectDeleteRequest>();

                var stringContent = new StringContent(JsonSerializer.Serialize(deletebody), Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Delete, PATH);

                request.Content = stringContent;

                var response = await client.SendAsync(request);

                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
                var responseContent = await response.Content.ReadAsStringAsync();

                var result = JsonSerializer.Deserialize<IReadOnlyList<DependencyResponse>>(responseContent, options);
                foreach (var item in result)
                {
                    item.StatusCode.Should().Be(400);
                    item.StatusMsg.Should().Be("The BaseProject cannot be deleted because it is linked to one or more Reporting Projects. Note: Check BaseProject properties on DWH Builder, as these features are in development for BuilderX.");

                }
                response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
                
            }
        }

        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_Returns_Success_Response()
        {

            using (var scope = _factory.Services.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var client = _factory.CreateClient();

                client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.ResetCorrectionTypeId, 3).With(x => x.CountryId, 15));
               

                var typeId = 2;
                var countryIds = "86";
                var countryIdsList = countryIds.Split(',').Select(int.Parse).ToArray();
                var dbContextResponseCount = dbContext.BaseProjects
                    .Count(x => countryIdsList.Contains(x.CountryId) && x.TypeId == typeId);

                client.DefaultRequestHeaders.Add("Custom-Countryid", countryIds);

                // Act
                var response = await client.GetAsync($"{PATH}/list?typeId={typeId}");

                var responseContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var result = JsonSerializer.Deserialize<BaseProjectNameandIdListResponse>(responseContent, options);

                // Assert
                response.StatusCode.Should().Be(HttpStatusCode.OK);
                responseContent.Should().NotBeNull();
                result.Should().NotBeNull();
                result.Records.Count.Should().Be(dbContextResponseCount);

            }
           
        }


        [Fact]
        public async Task GetAsyncListBaseProjectsByNameandId_When_EmptyResponse_Returns_NotFound_With_ProblemDetails()
        {
            // Arrange
            var client = _factory.CreateClient();
            var typeId = 999; // Non-existent TypeId for testing
            client.DefaultRequestHeaders.Add("Custom-Countryid", "-1"); // Non-existent CountryIds for testing

            // Act
            var response = await client.GetAsync($"{PATH}/list?typeId={typeId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            var responseContent = await response.Content.ReadAsStringAsync();
            responseContent.Should().NotBeNullOrEmpty();

            var problemDetails = JsonSerializer.Deserialize<ProblemDetails>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            problemDetails.Should().NotBeNull();
            problemDetails.Status.Should().Be(404);
            problemDetails.Title.Should().Be("ENTITY_NOT_EXISTS");
        }

        [Fact]
        public async Task DeleteAsync_BaseProject_Returns_BadRequest_For_QC_Status()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var client = _factory.CreateClient();
                var dbContext = scope.ServiceProvider.GetRequiredService<OracleDbContext>();
                var qcStatusBP = await dbContext.QCStatus.Where(x => x.Deleted == 0 && x.Status == 6).Select(i => i.BaseProjectId).FirstOrDefaultAsync();
                // Setup delete request
                List<int> baseProjectIds = new List<int> { qcStatusBP };
                _fixture.Customize<BaseProjectDeleteRequest>(b => b.With(x => x.Ids, baseProjectIds));
                var deleteBody = _fixture.Create<BaseProjectDeleteRequest>();

                var stringContent = new StringContent(JsonSerializer.Serialize(deleteBody), Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Delete, PATH);
                request.Content = stringContent;
                client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                var response = await client.SendAsync(request);

                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
                var responseContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var result = JsonSerializer.Deserialize<IReadOnlyList<DependencyResponse>>(responseContent, options);
                foreach (var item in result)
                {
                    item.StatusCode.Should().Be((int)HttpStatusCode.BadRequest);
                    item.Dependency.QCStatusPeriodId.Should().NotBe(0);
                }

                response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
            }
        }

        [Fact]
        public async Task AddAsync_ValidBaseProject_Should_CreateOutboxItem()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var client = _factory.CreateClient();

            // ✅ Set Required Headers
            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            // ✅ Ensure Headers Match Controller
            client.DefaultRequestHeaders.Add("Custom-Countryid", "47");

            var request = new BaseProjectCreateRequest
            {
                Name = "CL_Toys INFANT & PRESCH 1w",
                TypeId = 1,
                PanelId = 1,
                DataTypeId = 1,
                PurposeId = 1,
                PeriodicityId = 2,
                CountryId = 47,
                ProductGroups = new List<BaseProjectProductGroupModelDto>
        {
            new BaseProjectProductGroupModelDto { ProductGroupId = 471384 }
        },
                Predecessors = new List<BaseProjectPredecessorModelDto>
        {
            new BaseProjectPredecessorModelDto { PredecessorId = 223873 } // Ensure valid Predecessor
        },
                IsRelevantForReportingEnabled = true,
                ResetCorrectionTypeId = 1,
                IsAutoLoad = false,
                SQCMode = 0,
                IsAutomatedPriceCheck = false
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // ✅ Act - Send API Request
            var response = await client.PostAsync("/api/v1/baseprojects", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            // ✅ Log API Response Details
            _output.WriteLine($"Response Status Code: {response.StatusCode}");
            _output.WriteLine($"Response Content: {responseContent}");
            _output.WriteLine($"Request Payload: {JsonSerializer.Serialize(request)}");

            // 🔴 If response is 500, log & fail immediately
            if (response.StatusCode == HttpStatusCode.InternalServerError)
            {
                throw new Exception($"API returned 500: {responseContent}");
            }

            // ✅ Assert Response Status
            response.StatusCode.Should().Be(HttpStatusCode.Created);

            // ✅ Fetch latest OutBoxItem
            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.BaseProjectCreate.ToString());

            // ✅ Assert OutBoxItem entry is created
            outboxItem.Should().NotBeNull();
            outboxItem.TypeId.Should().Be(ProjectMessageType.BaseProjectCreate.ToString());
            outboxItem.Status.Should().Be(OutboxStatus.Pending); // Expected status
        }


        [Fact]
        public async Task UpdateAsync_ValidBaseProject_Should_UpdateOutboxItem()
        {
            using var scope = _factory.Services.CreateScope();
            var serviceProvider = scope.ServiceProvider;
            var dbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
            var client = _factory.CreateClient();

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            var existingBaseProject = await dbContext.BaseProjects.FirstOrDefaultAsync();
            existingBaseProject.Should().NotBeNull(); // Ensure we have a valid base project

            var baseProjectId = existingBaseProject.Id; // Extract ID

            var baseProjectEditRequest = new BaseProjectEditRequest
            {
                Name = "Updated CL_Toys INFANT & PRESCH 1w",
                DataTypeId = 2, // New DataTypeId
                PurposeId = 2, // Updated Purpose
                IsRelevantForReportingEnabled = false, // Change boolean field
                Predecessors = new List<BaseProjectPredecessorModelDto>
        {
            new BaseProjectPredecessorModelDto { PredecessorId = 223873 }
        }
            };

            var content = new StringContent(JsonSerializer.Serialize(baseProjectEditRequest), Encoding.UTF8, "application/json");

            var response = await client.PutAsync($"/api/v1/baseprojects/{baseProjectId}", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            _output.WriteLine($"Response Status Code: {response.StatusCode}");
            _output.WriteLine($"Response Content: {responseContent}");
            _output.WriteLine($"Request Payload: {JsonSerializer.Serialize(baseProjectEditRequest)}");

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Should().NotBeNull();

            var updatedBaseProject = await dbContext.BaseProjects.FindAsync(baseProjectId);
            updatedBaseProject.Should().NotBeNull();
            updatedBaseProject.DataTypeId.Should().Be(baseProjectEditRequest.DataTypeId);
            updatedBaseProject.PurposeId.Should().Be(baseProjectEditRequest.PurposeId);
            updatedBaseProject.IsRelevantForReportingEnabled.Should().BeFalse();

            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.BaseProjectUpdate.ToString());

            outboxItem.Should().NotBeNull();
            outboxItem.TypeId.Should().Be(ProjectMessageType.BaseProjectUpdate.ToString());
            outboxItem.Status.Should().Be(OutboxStatus.Pending); // Expected status
        }


        [Fact]
        public async Task DeleteAsync_BaseProject_Should_CreateOutboxItem()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var client = _factory.CreateClient();

            var existingBaseProject = await dbContext.BaseProjects
                .OrderBy(bp => bp.Id) // Ensuring we get a valid existing project
                .FirstOrDefaultAsync();

            Assert.NotNull(existingBaseProject); // Ensure that the BaseProject exists

            var deleteRequest = new BaseProjectDeleteRequest
            {
                Ids = new List<int> { existingBaseProject.Id }
            };

            var deleteContent = new StringContent(JsonSerializer.Serialize(deleteRequest), Encoding.UTF8, "application/json");

            client.DefaultRequestHeaders.Add("userName", "testUser");

            var deleteRequestMessage = new HttpRequestMessage(HttpMethod.Delete, "/api/v1/baseprojects")
            {
                Content = deleteContent
            };

            var response = await client.SendAsync(deleteRequestMessage);

            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            response.Should().NotBeNull();

            var deletedBaseProject = await dbContext.BaseProjects
                .FirstOrDefaultAsync(bp => bp.Id == existingBaseProject.Id);

            deletedBaseProject.Should().BeNull(); // BaseProject should no longer exist

            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.BaseProjectDelete.ToString());

            outboxItem.Should().NotBeNull();
            outboxItem.TypeId.Should().Be(ProjectMessageType.BaseProjectDelete.ToString());
            outboxItem.Status.Should().Be(OutboxStatus.Pending); // Expected status
        }

        [Fact]
        public async Task UpdateAsync_WhenDeletedProductGroupsHaveDataOrRBBP_ThrowsInvalidOperationException()
        {
            using var scope = _factory.Services.CreateScope();
            var serviceProvider = scope.ServiceProvider;
            var dbContext = serviceProvider.GetRequiredService<PostgreSqlDbContext>();
            var oracledbContext = serviceProvider.GetRequiredService<OracleDbContext>();
            var client = _factory.CreateClient();

            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            var dataloadedbps = await oracledbContext.FactPdOutItms.FirstOrDefaultAsync();
            dataloadedbps.Should().NotBeNull("we need a data-loaded base project");

            // Arrange
            var existingBaseProject = await dbContext.BaseProjects
                .Include(bp => bp.ProductGroups)
                .FirstOrDefaultAsync(bp =>
                    bp.ProductGroups.Any() &&
                    bp.Id == dataloadedbps.BaseProjectId &&
                    bp.ProductGroups.Select(x => x.ProductGroupId).Contains(dataloadedbps.ProductGroupId));

            existingBaseProject.Should().NotBeNull();
            var baseProjectId = existingBaseProject!.Id;

            var originalPGs = existingBaseProject.ProductGroups.Select(pg => pg.ProductGroupId).ToList();
            var retainedPG = originalPGs.First();
            var removedPG = originalPGs.Skip(1).FirstOrDefault();
            removedPG.Should().NotBe(0, "there should be at least two product groups so we can remove one");

            var editRequest = new BaseProjectEditRequest
            {
                Name = existingBaseProject.Name,
                DataTypeId = existingBaseProject.DataTypeId,
                PurposeId = existingBaseProject.PurposeId,
                IsRelevantForReportingEnabled = existingBaseProject.IsRelevantForReportingEnabled,
                ProductGroups = new List<BaseProjectProductGroupModelDto>
                {
                    new() { ProductGroupId = retainedPG } // simulate deletion of removedPG
                },
                Predecessors = new List<BaseProjectPredecessorModelDto>()
            };

            var content = new StringContent(JsonSerializer.Serialize(editRequest), Encoding.UTF8, "application/json");

            // Act
            var response = await client.PutAsync($"/api/v1/baseprojects/{baseProjectId}", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            _output.WriteLine($"Status Code: {response.StatusCode}");
            _output.WriteLine($"Response Content: {responseContent}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest, "removing PGs with data or RBBP should result in a 400");

            ProductGroupIssueResponse result = null;
            try
            {
                result = JsonSerializer.Deserialize<ProductGroupIssueResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (JsonException ex)
            {
                _output.WriteLine($"Deserialization error: {ex.Message}");
                throw;
            }

            result.Should().NotBeNull();
            result.Issues.Should().NotBeNullOrEmpty();
            result.Issues.Should().Contain(i => i.ProductGroupId == removedPG);
            var removedIssue = result.Issues.First(i => i.ProductGroupId == removedPG);
            removedIssue.HasDataLoaded.Should().BeTrue();
            removedIssue.RbProjectIds.Should().NotBeEmpty();

        }




    }
}
