﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using DWH.ProjectServices.API.Domain.Models;
using GfK.Dwh.LoadMonitor.Api.StartupExtensions;
using System.Net;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Elastic.Apm.Api;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Domain.Enum;
using ProjectSubType = DWH.ProjectServices.API.Domain.Enum.ProjectSubType;
using DWH.ProjectServices.API.Services.Constants;
using Microsoft.Extensions.DependencyInjection;
using Polly;
using DWH.ProjectServices.API.Services.Helper.Interface;
using static Azure.Core.HttpHeader;
using Microsoft.AspNetCore.Http;
namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class BaseProjectRepository : IBaseProjectRepository
    {
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly OracleDbContext _oracledbContext;
        private readonly IMapper _mapper;
        private IRetailerSeperationRepository _retailerSeperationRepository;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public BaseProjectRepository(OracleDbContext oracledbContext, PostgreSqlDbContext postdbContext,
            IMapper mapper, IRetailerSeperationRepository retailerSeperationRepository, IServiceScopeFactory serviceScopeFactory)
        {
            _postdbContext = postdbContext;
            _oracledbContext = oracledbContext;
            _mapper = mapper;
            _retailerSeperationRepository = retailerSeperationRepository;
            _serviceScopeFactory = serviceScopeFactory;

        }

        public async Task<BaseProject> AddAsync(BaseProject BaseProject)
        {
            var newBaseProject = _mapper.Map<BaseProjectEntity>(BaseProject);
            newBaseProject.Deleted = false;
            newBaseProject.CreatedWhen = DateTimeOffset.UtcNow;
            _postdbContext.BaseProjects.Add(newBaseProject);
            await _postdbContext.SaveChangesAsync();

            if (newBaseProject.QCProjects != null)
            {
                foreach (var qcPeriod in newBaseProject.QCProjects.QCPeriods)
                {
                    foreach (var period in qcPeriod.Periods)
                    {
                        period.RefProjectId = newBaseProject.Id;
                    }
                }
            }
            await _postdbContext.SaveChangesAsync();
            return _mapper.Map<BaseProject>(newBaseProject);
        }


        public async Task<IEnumerable<BaseProject>> GetAllAsync(BaseProjectPredecessor bpPredecessorRequest)
        {

            var result = await _postdbContext.BaseProjects
                .Where(c => c.CountryId == bpPredecessorRequest.CountryId)
                .Where(c => c.PanelId == bpPredecessorRequest.PanelId)
                .ToListAsync();
            if (!result.Any())
            {
                throw new EntityNotExistsException($"No Base Project exists with Country Id {bpPredecessorRequest.CountryId}", "BaseProject", bpPredecessorRequest.CountryId);
            }
            return _mapper.Map<IEnumerable<BaseProject>>(result);


        }

        public async Task<BaseProject> UpdateAsync(int baseProjectId, BaseProject baseProjectEditReq,bool isPGDeleted)
        {
            var baseProject = await _postdbContext
                                    .BaseProjects
                                    .Include(b => b.ProductGroups)
                                    .Include(b => b.Predecessors)
                                    .SingleOrDefaultAsync(b => b.Id == baseProjectId);

            if (baseProject != null)
            {
                if (baseProject.Deleted == true && baseProjectEditReq.Deleted == false)
                {
                    baseProject.Deleted = false;
                    baseProject.DeletedWhen = null;
                    baseProject.DeletedBy = null;
                }

                baseProject.Name = baseProjectEditReq.Name;
                baseProject.DataTypeId = baseProjectEditReq.DataTypeId;
                baseProject.PurposeId = baseProjectEditReq.PurposeId;
                baseProject.IsRelevantForReportingEnabled = baseProjectEditReq.IsRelevantForReportingEnabled;
                baseProject.UpdatedWhen = DateTime.UtcNow;
                baseProject.UpdatedBy = baseProjectEditReq.UpdatedBy;


                if (baseProjectEditReq.Predecessors != null)
                {
                    baseProject.Predecessors = new List<BaseProjectPredecessorEntity>();
                    baseProject.Predecessors = _mapper.Map<ICollection<BaseProjectPredecessorEntity>>(baseProjectEditReq.Predecessors);
                }
                if (baseProjectEditReq.ProductGroups != null)
                {
                    baseProject.ProductGroups = new List<BaseProjectProductGroupEntity>();
                    baseProject.ProductGroups = _mapper.Map<ICollection<BaseProjectProductGroupEntity>>(baseProjectEditReq.ProductGroups);
                }

                if (isPGDeleted)
                {
                    //Remove all previous QC Periods
                    var qcprojectId = await _postdbContext.QCProjects
                                    .Where(p => p.BaseProjectId == baseProjectId)
                                    .Select(p=>p.Id)
                                    .SingleOrDefaultAsync();
                    var qcPeriods = await _postdbContext.QCPeriod
                                    .Where(p => p.QCProjectId == qcprojectId)
                                    .ToListAsync();
                    _postdbContext.QCPeriod.RemoveRange(qcPeriods);

                    baseProject.QCProjects = new QCProjectEntity();
                    baseProject.QCProjects = _mapper.Map<QCProjectEntity>(baseProjectEditReq.QCProjects);
                }
                
                await _postdbContext.SaveChangesAsync();
                return _mapper.Map<BaseProject>(baseProject);
            }
            else
            {
                throw new EntityNotExistsException($"No Base Project exists with Id {baseProjectId}",
                                                    "BaseProject", baseProjectId);
            }
        }

        public async Task<BaseProject> GetAsync(int baseProjectId)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var baseProjectIds = dbContext.BaseProjects.IgnoreQueryFilters().Where(b => b.Deleted == true)
                                                               .Select(b => b.Id).ToList();

            var baseProject = await dbContext.BaseProjects.Include(b => b.QCProjects).Include(b => b.Predecessors
                                                                  .Where(b => !baseProjectIds.Contains(b.PredecessorId)))
                                                                  .Include(b => b.ProductGroups)
                                                                  .SingleOrDefaultAsync(b => b.Id == baseProjectId);

            if (baseProject is null)
                throw new EntityNotExistsException($"No Base Project exists with Id {baseProjectId}", "BaseProject", baseProjectId);

            baseProject.CreatedWhen = baseProject.CreatedWhen.UtcDateTime;
            var baseProjectResponse = _mapper.Map<BaseProject>(baseProject);

            return baseProjectResponse;

        }

        public async Task<IEnumerable<BaseProject>> GetAsyncList(BaseProjectsLists baseProjectListsRequest)
        {
            var result = await _postdbContext.BaseProjects
                .IgnoreQueryFilters() 
                .Where(bp => bp.Deleted == baseProjectListsRequest.Deleted)
                .WhereIfExists(i => baseProjectListsRequest.CountryIds.ToList().Contains(i.CountryId), baseProjectListsRequest.CountryIds)
                .WhereIfExists(i => baseProjectListsRequest.PeriodicityIds.ToList().Contains(i.PeriodicityId), baseProjectListsRequest.PeriodicityIds)
                .WhereIfExists(i => baseProjectListsRequest.PanelIds.ToList().Contains(i.PanelId), baseProjectListsRequest.PanelIds)
                .WhereIfExists(i => baseProjectListsRequest.DataTypeIds.ToList().Contains(i.DataTypeId), baseProjectListsRequest.DataTypeIds)
                .WhereIfExists(i => baseProjectListsRequest.PurposeIds.ToList().Contains(i.PurposeId), baseProjectListsRequest.PurposeIds)
                .WhereIfExists(i => baseProjectListsRequest.TypeIds.ToList().Contains(i.TypeId), baseProjectListsRequest.TypeIds)
                
                .WhereIfExists(i => baseProjectListsRequest.Usernames.ToList().Contains(i.CreatedBy) || baseProjectListsRequest.Usernames.ToList().Contains(i.UpdatedBy), baseProjectListsRequest.Usernames)
                .Where(i => baseProjectListsRequest.StartingDate == null || i.CreatedWhen >= baseProjectListsRequest.StartingDate || i.UpdatedWhen >= baseProjectListsRequest.StartingDate)
                .Where(i => baseProjectListsRequest.EndingDate == null || i.CreatedWhen < baseProjectListsRequest.EndingDate || i.UpdatedWhen < baseProjectListsRequest.EndingDate)
                .WhereIfExists(i => baseProjectListsRequest.BaseProjectIDs.ToList().Contains(i.Id), baseProjectListsRequest.BaseProjectIDs)
                .WhereIfExists(i => baseProjectListsRequest.QCProjectIDs.ToList().Contains(i.QCProjects.Id), baseProjectListsRequest.QCProjectIDs)
                .Where(i =>
                    string.IsNullOrWhiteSpace(baseProjectListsRequest.Name) || i.Name.ToUpper().Contains(baseProjectListsRequest.Name.ToUpper())
                    || baseProjectListsRequest.Id == 0 || i.Id == baseProjectListsRequest.Id
                    || i.QCProjects.Id == baseProjectListsRequest.Id
                )
                .Include(b => b.QCProjects)
                .Include(b => b.Predecessors)  
                .Include(b => b.ProductGroups.OrderBy(b=>b.ProductGroupId))
                .Include(b => b.DataType)
                .Include(b => b.Purpose)
                .WhereIfExists(i => i.ProductGroups.Any(pg => baseProjectListsRequest.ProductGroupIds.Contains(pg.ProductGroupId)), baseProjectListsRequest.ProductGroupIds)
                .OrderByDescending(i => i.Id) // Order by descending based on Id
                .Take(baseProjectListsRequest.Limit != 0 ? baseProjectListsRequest.Limit : 1000) // Limit the results to 1000
                .ToListAsync();

            if (!result.Any())
                throw new EntityNotExistsException("No base projects found", "BaseProjects");

            return _mapper.Map<IEnumerable<BaseProject>>(result);
        }
          
        public async Task<IReadOnlyList<Dependencies>> DeleteAsync(List<int> baseProjectIds, string deletedBy)
        {
            var responses = new List<Dependencies>();

            foreach (var bpId in baseProjectIds)
            {
                try
                {
                    var baseProject = await _postdbContext.BaseProjects.SingleOrDefaultAsync(b => b.Id == bpId);
                    var qcProject = await _postdbContext.QCProjects.SingleOrDefaultAsync(b => b.BaseProjectId == bpId);
                    var referencePeriods = await _postdbContext.Period.Where(p => p.RefProjectId == bpId).ToListAsync();

                    if (baseProject != null)
                    {
                        baseProject.Deleted = true;
                        baseProject.DeletedWhen = DateTime.UtcNow;
                        baseProject.DeletedBy = deletedBy;
                        responses.Add(new Dependencies(bpId.ToString(), qcProject?.Id.ToString(), (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString(), null));
                    }
                    else
                    {
                        responses.Add(new Dependencies(bpId.ToString(), qcProject?.Id.ToString(), (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString(), null));
                    }

                    if (referencePeriods.Any())
                    {
                        _postdbContext.Period.RemoveRange(referencePeriods);
                    }
                }
                catch (Exception ex)
                {
                    responses.Add(new Dependencies(bpId.ToString(), string.Empty, (int)HttpStatusCode.BadRequest, HttpStatusCode.BadRequest.ToString(), null));
                }
            }

            try
            {
                await _postdbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log the exception
                responses.Add(new Dependencies(baseProjectIds.ToString(), string.Empty, (int)HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError.ToString(), null));
            }

            return responses;
        }

        public async Task<ProjectsDependencies> CheckDependencies(int baseProjectId)
        {
            var reportingProjectIds = await _oracledbContext.ReportingProjects
                     .Where(rp => rp.Deleted == 0 &&
                                  _oracledbContext.ReportingBaseProjects
                                      .IgnoreQueryFilters()
                                      .Where(b => b.Deleted == 0 && b.BaseProjectId == baseProjectId)
                                      .Select(b => b.Id)
                                      .Contains(rp.Id))
                     .Select(b => b.Id)
                     .ToArrayAsync();

            var productionProjectIds = await _oracledbContext.ProductionProjects
                    .Where(rp => rp.Deleted == 0 &&
                                 _oracledbContext.ProductionBaseProjects
                                     .IgnoreQueryFilters()
                                     .Where(b => b.Deleted == 0 && b.BaseProjectId == baseProjectId)
                                     .Select(b => b.Id)
                                     .Contains(rp.Id))
                    .Select(b => b.Id)
                    .ToArrayAsync();

            var rbBaseProjectIds = await _oracledbContext.RBProjects
                  .Where(rp => rp.Deleted == 0 &&
                               _oracledbContext.RBBaseProjects
                                   .IgnoreQueryFilters()
                                   .Where(b => b.Deleted == 0 && b.BaseProjectId == baseProjectId)
                                   .Select(b => b.Id)
                                   .Contains(rp.Id))
                  .Select(b => b.Id)
                  .ToArrayAsync();

            return new ProjectsDependencies
            {
                ReportingProjectIds = reportingProjectIds.Any() ? reportingProjectIds : Array.Empty<int>(),
                ProductionProjectIds = productionProjectIds.Any() ? productionProjectIds : Array.Empty<int>(),
                RBBaseProjectIds = rbBaseProjectIds.Any() ? rbBaseProjectIds : Array.Empty<int>()
            };
        }

        public async Task<BaseProject> GetBaseProjectAsync(int qcProjectId)
        {
            var baseProject = await _postdbContext
            .BaseProjects
            .SingleOrDefaultAsync(b => b.QCProjects.Id == qcProjectId);

            if (baseProject is null)
                throw new EntityNotExistsException($"No Base Project exists with qcProjectId {qcProjectId}");

            var baseProjectResponse = _mapper.Map<BaseProject>(baseProject);
            return baseProjectResponse;

        }



        public BaseProjectEntity CreateRetailerBaseProject(BaseProjectEntity existingBaseProject,string username)
        {
            var newBaseProject = new BaseProjectEntity
            {
                Name = existingBaseProject.Name,
                TypeId = (int)ProjectSubType.Retailer,
                PanelId = existingBaseProject.PanelId,
                DataTypeId = existingBaseProject.DataTypeId,
                PurposeId = existingBaseProject.PurposeId,
                PeriodicityId = existingBaseProject.PeriodicityId,
                CountryId = existingBaseProject.CountryId,
                IsRelevantForReportingEnabled = existingBaseProject.IsRelevantForReportingEnabled,
                CreatedBy = username,
                CreatedWhen = DateTimeOffset.UtcNow,
                UpdatedBy = "",
                UpdatedWhen = null,
                DeletedBy = existingBaseProject.DeletedBy,
                DeletedWhen = existingBaseProject.DeletedWhen,
                Deleted = existingBaseProject.Deleted,
                ProductGroups = MapProductGroups(existingBaseProject.ProductGroups),
                Predecessors = MapPredecessors(existingBaseProject.Predecessors)
            };

            if (existingBaseProject.QCProjects != null)
            {
                newBaseProject.QCProjects = AddQCProject(existingBaseProject, username);
            }

            return newBaseProject;
        }

        private List<BaseProjectProductGroupEntity> MapProductGroups(IEnumerable<BaseProjectProductGroupEntity> productGroups)
        {
            return productGroups?
                .Select(pg => new BaseProjectProductGroupEntity
                {
                    ProductGroupId = pg.ProductGroupId
                }).ToList() ?? new List<BaseProjectProductGroupEntity>();
        }

        private List<BaseProjectPredecessorEntity> MapPredecessors(IEnumerable<BaseProjectPredecessorEntity> predecessors)
        {
            return predecessors?.Select(p => new BaseProjectPredecessorEntity
            {
                PredecessorId = p.PredecessorId
            }).ToList() ?? new List<BaseProjectPredecessorEntity>();
        }

        private QCProjectEntity AddQCProject(BaseProjectEntity existingBaseProject,string username)
        {
            var newQCProject = new QCProjectEntity
            {
                ResetCorrectionTypeId = 1,
                IsAutoLoad = true,
                SQCMode = 0,
                IsAutomatedPriceCheck = existingBaseProject.PanelId == 1 ? false : null,
                QCPeriods = new List<QCPeriodEntity>()
            };

            AddQCPeriods(existingBaseProject.QCProjects, newQCProject,username);

            return newQCProject;
        }

        private void AddQCPeriods(QCProjectEntity existingQCProject, QCProjectEntity newQCProject,string username)
        {
            foreach (var qcPeriod in existingQCProject.QCPeriods)
            {
                var newQCPeriod = new QCPeriodEntity
                {
                    CreatedBy = username,
                    CreatedWhen = DateTimeOffset.UtcNow,
                    UpdatedBy = null,
                    UpdatedWhen = null,
                    PeriodId = qcPeriod.PeriodId,
                    Periods = qcPeriod.Periods?.Select(p => new PeriodEntity
                    {
                        RefProjectId = p.RefProjectId,
                        RefPeriodId = p.RefPeriodId,
                        index = p.index
                    }).ToList() ?? new List<PeriodEntity>(),
                    StockInitialization = new StockInitializationEntity
                    {
                        StockBaseProjectId = qcPeriod?.StockInitialization?.StockBaseProjectId,
                        StockPeriodId = qcPeriod?.StockInitialization?.StockPeriodId
                    }
                };

                newQCProject.QCPeriods.Add(newQCPeriod);
            }
        }


        public async Task<BaseProjectEntity> UpdateTypeIdAsync(int baseProjectId, int TypeId, string username, PostgreSqlDbContext dbContext)
        {
            var baseProject = await dbContext
                                    .BaseProjects
                                    .Include(b => b.ProductGroups.OrderBy(i => i.ProductGroupId))
                                    .Include(b => b.Predecessors)
                                    .Include(b => b.QCProjects)
                                    .ThenInclude(qc => qc.QCPeriods)
                                    .ThenInclude(qcPeriod => qcPeriod.Periods)
                                     .Include(b => b.QCProjects)
                                   .ThenInclude(qc => qc.QCPeriods)
                                    .ThenInclude(qcPeriod => qcPeriod.StockInitialization)
                                    .FirstOrDefaultAsync(b => b.Id == baseProjectId);

            if (baseProject == null)
            {
                throw new EntityNotExistsException($"No Base Project exists with Id {baseProjectId}",
                                                    "BaseProject", baseProjectId);
            }

            baseProject.TypeId = TypeId;
            baseProject.UpdatedBy = username;
            baseProject.UpdatedWhen = DateTimeOffset.UtcNow;
            return baseProject;
        }

        public async Task<IReadOnlyList<BaseProjectNameandIds>> GetAsyncListBaseProjects(BaseProjectNameandIdLists baseProjectListsRequest)
        {
            var result = await _postdbContext.BaseProjects
                .WhereIfExists(i => baseProjectListsRequest.CountryIds.ToList().Contains(i.CountryId), baseProjectListsRequest.CountryIds)
                .Where(i => i.TypeId == baseProjectListsRequest.TypeId)
                .GroupJoin(
                    _postdbContext.RetailerSeperationRequests
                        .Where(rsr => rsr.RequestStatusId == baseProjectListsRequest.StatusId)
                        .SelectMany(rsr => rsr.RetailerSeperations.Select(rs => rs.SourceBPId)),
                    bp => bp.Id,
                    pendingId => pendingId,
                    (bp, pendingIds) => new { bp, pendingExists = pendingIds.Any() }
                )
                .Where(joined => !joined.pendingExists)
                .Select(joined => joined.bp)
                .OrderByDescending(bp => bp.Id)
                .ToListAsync();

            if (!result.Any())
                throw new EntityNotExistsException("No base projects found", "BaseProjects");

            return _mapper.Map<IReadOnlyList<BaseProjectNameandIds>>(result);
        }

        public async Task<List<int>> GetBaseProjectWithQCStatus(QCStatuses qcStatuses)
        {
            var qcBaseProjectIds = await _oracledbContext.QCStatus
                .IgnoreQueryFilters()
                .Where(b => b.Deleted == 0 && qcStatuses.BaseProjectIds.Contains(b.BaseProjectId) && b.Status==QCStatusConstants.QCStatus)
                .WhereIfExists(b=> qcStatuses.ReportingPeriodIds.Contains(b.RepPeriodId),qcStatuses.ReportingPeriodIds)
                .Select(b => b.BaseProjectId)
                .ToListAsync();
            if (qcBaseProjectIds.Any())
            {
                return qcBaseProjectIds;
            }
            return qcBaseProjectIds;

        }

        public async Task<List<long>> GetQCPeriodWithQCStatus(QCStatuses qcStatuses)
        {
            var qcBaseProjectIds = await _oracledbContext.QCStatus
                .IgnoreQueryFilters()
                .Where(b => b.Deleted == 0 && qcStatuses.BaseProjectIds.Contains(b.BaseProjectId) && b.Status == QCStatusConstants.QCStatus)
                .WhereIfExists(b => qcStatuses.ReportingPeriodIds.Contains(b.RepPeriodId), qcStatuses.ReportingPeriodIds)
                .Select(b => b.RepPeriodId)
                .ToListAsync();
            if (qcBaseProjectIds.Any())
            {
                return qcBaseProjectIds;
            }
            return qcBaseProjectIds;

        }


        public async Task<bool> CheckRetailerSeparationState(int baseProjectsId)
        {
            var baseProject = await GetAsync(baseProjectsId);
            if (baseProject.TypeId != (int)ProjectSubType.IndustryRetailer)
                return false;

            var baseProjectInPendingState = await _retailerSeperationRepository.GetRetailerSeperationRequestIdsBySourceBpIdAsync(baseProjectsId);

            if (baseProjectInPendingState.Count > 0)
                return true;

            return false;

        }

        public async Task<List<int>> GetByBaseProjectIdAsync(BaseProjectCountries baseProjectCountryRequest)
        {
            var baseProjects = await _postdbContext.BaseProjects
                .Where(b => baseProjectCountryRequest.BaseProjectIds.Contains(b.Id)
                    && baseProjectCountryRequest.CountryIds.Contains(b.CountryId)
                    && b.Deleted == false)
                .Select(b => b.Id)
                .Distinct()
                .ToListAsync();
            return baseProjects;
        }
        public async Task<int[]> CheckDataLoading(List<int> productGroups, int baseProjectId)
        {
            var pgsWithData = new List<int>();

            foreach (var productGroup in productGroups)
            {
                var hasData = await _oracledbContext.FactPdOutItms
                    .FirstOrDefaultAsync(rp => rp.BaseProjectId == baseProjectId && rp.ProductGroupId == productGroup);

                if (hasData!= null)
                {
                    pgsWithData.Add(productGroup);
                }
            }

            return pgsWithData.ToArray();
        }

        public async Task<List<RBPGDependencies>> CheckProductGroupRBBPDependency(List<int> productGroups, int baseProjectId)
        {
            var result = await (
                from p in _oracledbContext.ProductGroups
                join pb in _oracledbContext.PGProjects on p.Id equals pb.Id
                join bp in _oracledbContext.RBBaseProjects on pb.ProjectId equals bp.BaseProjectId
                join prj in _oracledbContext.RBProjects on bp.Id equals prj.Id
                where productGroups.Contains(p.Id)
                    && bp.BaseProjectId == baseProjectId
                    && bp.Deleted == 0
                    && prj.Deleted == 0
                select new RBPGDependencies
                {
                    BaseProjectId = bp.BaseProjectId,
                    ProductGroupId = p.Id,
                    ProductGroupDesc = p.Description,
                    RbProjectId = prj.Id
                }
            ).ToListAsync();
            return result;
        }

        public async Task<int> GetPeriodicityIdAsync(int baseProjectId)
        {
            return await _postdbContext.BaseProjects
                .Where(bp => bp.Id == baseProjectId)
                .Select(bp => bp.PeriodicityId)
                .FirstOrDefaultAsync();
        }


        public async Task<IEnumerable<QCPeriod>> GetQCPeriodAsync(int baseProjectId)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var qcPeriods = await dbContext.BaseProjects
            .Where(b => b.Id == baseProjectId)
            .SelectMany(b => b.QCProjects.QCPeriods)
            .ToListAsync();

            return _mapper.Map<IEnumerable<QCPeriod>>(qcPeriods);

        }

        public async Task<IReadOnlyList<string>> GetUsersList()
        {
            var names = await _postdbContext.BaseProjects
                        .Select(x => new { x.CreatedBy, x.UpdatedBy, x.DeletedBy })
                        .ToListAsync();

            var result = names
                .SelectMany(x => new[] { x.CreatedBy, x.UpdatedBy, x.DeletedBy })
                .Where(name => !string.IsNullOrWhiteSpace(name))
                .Distinct()
                .OrderBy(name => name)
                .ToList();



            if (!result.Any())
                throw new EntityNotExistsException("No base projects found", "BaseProjects");

            return result;
        }

        public async Task<IReadOnlyList<Dependencies>> AddBulkAsync(List<int> baseProjectIds, string userName)
        {
            var responses = new List<Dependencies>();

            // Get all source BaseProjects with all related data in one database call
            var sourceBaseProjects = await _postdbContext.BaseProjects
                .Include(b => b.QCProjects)
                    .ThenInclude(qc => qc.QCPeriods)
                        .ThenInclude(qcp => qcp.Periods)
                .Include(b => b.QCProjects)
                    .ThenInclude(qc => qc.QCPeriods)
                        .ThenInclude(qcp => qcp.StockInitialization)
                .Include(b => b.Predecessors)
                .Include(b => b.ProductGroups)
                .Where(b => baseProjectIds.Contains(b.Id))
                .ToListAsync();

            // Check for missing BaseProjects and add error responses
            var foundIds = sourceBaseProjects.Select(bp => bp.Id).ToList();
            var missingIds = baseProjectIds.Except(foundIds).ToList();

            foreach (var missingId in missingIds)
            {
                responses.Add(new Dependencies(
                    missingId.ToString(),
                    string.Empty,
                    StatusCodes.Status404NotFound,
                    $"Source BaseProject with ID {missingId} not found.",
                    null));
            }

            // Process each source BaseProject to create new ones
            foreach (var sourceBaseProject in sourceBaseProjects)
            {
                try
                {

                    var newBaseProject = new BaseProjectEntity
                    {
                        Name = sourceBaseProject.Name,
                        TypeId = sourceBaseProject.TypeId,
                        PanelId = sourceBaseProject.PanelId,
                        IsRelevantForReportingEnabled = sourceBaseProject.IsRelevantForReportingEnabled,
                        DataTypeId = sourceBaseProject.DataTypeId,
                        PurposeId = sourceBaseProject.PurposeId,
                        PeriodicityId = sourceBaseProject.PeriodicityId,
                        CountryId = sourceBaseProject.CountryId,
                        //ResetCorrectionTypeId = sourceBaseProject.ResetCorrectionTypeId,
                        //IsAutoLoad = sourceBaseProject.IsAutoLoad,
                        //SQCMode = sourceBaseProject.SQCMode,
                        //IsAutomatedPriceCheck = sourceBaseProject.IsAutomatedPriceCheck,
                        CreatedBy = userName,
                        UpdatedBy = userName,
                        CreatedWhen = DateTimeOffset.UtcNow,
                        Deleted = false
                    };

                    if (sourceBaseProject.ProductGroups != null)
                    {
                        newBaseProject.ProductGroups = sourceBaseProject.ProductGroups.Select(pg => new BaseProjectProductGroupEntity
                        {
                            ProductGroupId = pg.ProductGroupId,
                            Deleted = false
                        }).ToList();
                    }

                    if (sourceBaseProject.Predecessors != null)
                    {
                        newBaseProject.Predecessors = sourceBaseProject.Predecessors.Select(p => new BaseProjectPredecessorEntity
                        {
                            PredecessorId = p.PredecessorId
                        }).ToList();
                    }

                    if (sourceBaseProject.QCProjects != null && sourceBaseProject.PanelId != (int)PanelType.IDAS)
                    {
                        newBaseProject.QCProjects = new QCProjectEntity
                        {
                            ResetCorrectionTypeId = sourceBaseProject.QCProjects.ResetCorrectionTypeId,
                            IsAutoLoad = sourceBaseProject.QCProjects.IsAutoLoad,
                            SQCMode = sourceBaseProject.QCProjects.SQCMode,
                            IsAutomatedPriceCheck = sourceBaseProject.QCProjects.IsAutomatedPriceCheck,
                            QCPeriods = sourceBaseProject.QCProjects.QCPeriods?.Select(qcp => new QCPeriodEntity
                            {
                                PeriodId = qcp.PeriodId,
                                CreatedBy = userName,
                                UpdatedBy = userName,
                                CreatedWhen = DateTimeOffset.UtcNow,
                                Periods = qcp.Periods?.Select(p => new PeriodEntity
                                {
                                    RefPeriodId = p.RefPeriodId,
                                    index = p.index
                                }).ToList(),
                                StockInitialization = qcp.StockInitialization != null ? new StockInitializationEntity
                                {
                                    StockBaseProjectId = qcp.StockInitialization.StockBaseProjectId,
                                    StockPeriodId = qcp.StockInitialization.StockPeriodId
                                } : null
                            }).ToList()
                        };
                    }

                    _postdbContext.BaseProjects.Add(newBaseProject);
                    await _postdbContext.SaveChangesAsync();

                    if (newBaseProject.QCProjects?.QCPeriods != null)
                    {
                        foreach (var qcPeriod in newBaseProject.QCProjects.QCPeriods)
                        {
                            if (qcPeriod.Periods != null)
                            {
                                foreach (var period in qcPeriod.Periods)
                                {
                                    period.RefProjectId = newBaseProject.Id;
                                }
                            }
                        }
                        await _postdbContext.SaveChangesAsync();
                    }

                    responses.Add(new Dependencies(
                        sourceBaseProject.Id.ToString(),
                        newBaseProject.Id.ToString(),
                        StatusCodes.Status201Created,
                        "BaseProject created successfully.",
                        null));
                }
                catch (Exception ex)
                {
                    responses.Add(new Dependencies(
                        sourceBaseProject.Id.ToString(),
                        string.Empty,
                        StatusCodes.Status500InternalServerError,
                        $"Internal server error: {ex.Message}",
                        null));
                }
            }

            return responses;
        }
    }
}

