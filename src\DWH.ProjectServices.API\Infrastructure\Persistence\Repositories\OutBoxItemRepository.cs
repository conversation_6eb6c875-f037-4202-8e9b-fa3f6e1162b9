﻿using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class OutBoxItemRepository: IOutBoxItemRepository
    {
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public OutBoxItemRepository(PostgreSqlDbContext postdbContext, IServiceScopeFactory serviceScopeFactory)
        {
            _postdbContext = postdbContext;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task SaveMessagesAsync(object message, ProjectMessageType messageTypeId)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var outBoxItem = new OutBoxItemEntity
            {
                Payload = System.Text.Json.JsonSerializer.Serialize(message),
                TypeId = messageTypeId.ToString(),
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow
            };

            dbContext.Add(outBoxItem);
            await dbContext.SaveChangesAsync();
        }

        public async Task SaveBulkMessagesAsync(List<(object message, ProjectMessageType messageTypeId)> messages)
        {
            if (!messages.Any()) return;

            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var outBoxItems = messages.Select(m => new OutBoxItemEntity
            {
                Payload = System.Text.Json.JsonSerializer.Serialize(m.message),
                TypeId = m.messageTypeId.ToString(),
                Status = OutboxStatus.Pending,
                CreatedAt = DateTime.UtcNow
            }).ToList();

            dbContext.AddRange(outBoxItems);
            await dbContext.SaveChangesAsync();
        }

        public async Task<List<OutBoxItemEntity>> GetUnprocessedMessagesAsync()
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            return await dbContext.OutBoxItem
                .Where(m => m.Status == OutboxStatus.Pending)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();
        }

        public async Task MarkMessageAsProcessedAsync(Guid messageId)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var message = await dbContext.OutBoxItem.FindAsync(messageId);
            if (message != null)
            {
                message.Status = OutboxStatus.Processed;
                message.ProcessedAt = DateTime.UtcNow;
                await dbContext.SaveChangesAsync();
            }
        }
    }
}
