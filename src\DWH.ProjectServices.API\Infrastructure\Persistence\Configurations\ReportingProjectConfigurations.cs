﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class ReportingProjectConfigurations : IEntityTypeConfiguration<ReportingProject>
    {
        public void Configure(EntityTypeBuilder<ReportingProject> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_REPPROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("REPPROJECTID");
            builder.Property(p => p.Deleted).HasColumnName("DELETED");

        }
    }
}